#!/bin/bash

# 设置数据目录和模型保存目录
DATA_DIR="/data/datasets/processed_datasets/movielens"
LOG_DIR="/data/datasets/processed_datasets/movielens/embedding_analysis/original"

# 数据目录包含所有需要的嵌入和映射
# 确保目录结构正确，包含feature_embeddings, graph_embeddings, text_embeddings等子目录

# 创建保存目录
mkdir -p $LOG_DIR

# 切换到正确的目录
cd /root/code

# 运行CTR模型训练（使用原始embeddings）
echo "Running CTR model with original embeddings..."
python -m GraphLLM4CTR.ablation_study_losses.ctr_with_original_embeddings_fixed \
    --train_path $DATA_DIR/train.csv \
    --val_path $DATA_DIR/val.csv \
    --test_path $DATA_DIR/test.csv \
    --data_dir $DATA_DIR \
    --item_meta_path $DATA_DIR/train.csv \
    --epochs 10 \
    --batch_size 256 \
    --lr 0.002 \
    --weight_decay 1e-5 \
    --early_stop_patience 5 \
    --warmup_steps 500 \
    --max_grad_norm 1.0 \
    --seed 42 \
    --log_dir $LOG_DIR \
    --use_wandb

echo "Training completed!"
