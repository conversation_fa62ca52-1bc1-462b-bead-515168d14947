# GraphLLM4CTR 损失函数消融实验

本目录包含用于进行GraphLLM4CTR模型损失函数消融实验的代码。这些实验旨在评估不同损失函数组合对模型性能的影响。

## 损失函数分组

损失函数被分为三个主要组：

1. **User-Item Graph Loss**：
   - User-Item Loss (λ1)：用户-物品交互损失

2. **User Graph Loss**：
   - User Structure Loss (λ2)：用户图结构损失
   - User Text-Graph Loss (λ4)：用户文本-图对齐损失

3. **Item Graph Loss**：
   - Item Structure Loss (λ3)：物品图结构损失
   - Item Text-Graph Loss (λ5)：物品文本-图对齐损失

## 消融实验类型

我们进行以下6种消融实验：

1. **w/o user-item graph loss**：移除 λ1
2. **w/o user graph loss**：移除 λ2 和 λ4
3. **w/o item graph loss**：移除 λ3 和 λ5
4. **w/o user-item graph loss & item graph loss**：移除 λ1, λ3 和 λ5
5. **w/o user-item graph loss & user graph loss**：移除 λ1, λ2 和 λ4
6. **w/o user graph loss & item graph loss**：移除 λ2, λ3, λ4 和 λ5

注意：我们不进行"移除所有损失函数"的实验，因为这样模型将没有任何学习目标。作为替代，在后续CTR模型训练中，可以直接使用原始embedding作为基线进行比较。

## 文件说明

- `ablation_contrastive_model.py`：实现了支持不同消融设置的对比学习模型
- `ablation_train.py`：训练脚本，用于训练特定消融设置的模型
- `run_all_ablations.sh`：运行所有消融实验的脚本
- `qformer.py`：从原始代码复制的QFormer实现
- `optimized_batch_builder_for_check.py`：从原始代码复制的批次构建器

## 使用方法

### 运行单个消融实验

```bash
python ablation_train.py \
    --data_dir /path/to/data \
    --model_save_dir /path/to/save/models \
    --batch_size 128 \
    --num_epochs 3 \
    --learning_rate 5e-4 \
    --temperature 0.05 \
    --ablation_type "wo_user_item" \
    --wandb_project "GraphLLM4CTR_Ablation_Study"
```

可用的`ablation_type`选项：
- `none`：使用所有损失函数（基线）
- `wo_user_item`：不使用用户-物品损失
- `wo_user_graph`：不使用用户图损失
- `wo_item_graph`：不使用物品图损失
- `wo_user_item_and_item_graph`：不使用用户-物品损失和物品图损失
- `wo_user_item_and_user_graph`：不使用用户-物品损失和用户图损失
- `wo_user_graph_and_item_graph`：不使用用户图损失和物品图损失

### 运行所有消融实验

```bash
./run_all_ablations.sh
```

这将按顺序运行所有消融实验，并将结果保存到指定目录。

## 结果分析

每个实验的结果将保存在`model_save_dir`下的相应子目录中，包括：

1. 模型检查点
2. 对齐的嵌入向量
3. 训练日志
4. Weights & Biases记录（如果启用）

通过比较不同消融设置下的性能，我们可以评估每种损失函数组对模型整体性能的贡献。
