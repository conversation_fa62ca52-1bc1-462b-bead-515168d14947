#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CTR模型训练脚本，使用原始embeddings而不是对比学习后的结果
"""

import os
import sys
import json
import time
import torch
import random
import logging
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import ast
from tqdm import tqdm
from datetime import datetime
from torch import nn
from torch.utils.data import Dataset, DataLoader
from transformers import get_linear_schedule_with_warmup, AutoTokenizer, AutoModel
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score

# 添加父目录到路径，以便导入原始模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from models.llm_hgt_layer_res import ResLLMHGTEncoder
from models.llm_rgcn_layer_res import ResLLMRGCNEncoder
from models.expert_fusion_focused import FocusedHybridExpertAdaptor
from models.qformer import TextQFormer
from models.feature_embedding import FeatureEmbedder, process_features

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入wandb，如果不可用则使用空对象
try:
    import wandb
except ImportError:
    class WandbDummy:
        def init(self, *args, **kwargs):
            logger.warning("Wandb not installed. Metrics will not be logged to Weights & Biases.")
            return self
        def log(self, *args, **kwargs):
            pass
        def finish(self):
            pass
    wandb = WandbDummy()

# 设置随机种子
def set_seed(seed):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

# 数据集类
class CTRDataset(Dataset):
    def __init__(self, df):
        self.user_ids = df['user_id'].values
        self.item_ids = df['movie_id'].values
        self.labels = df['label'].values

    def __len__(self):
        return len(self.user_ids)

    def __getitem__(self, idx):
        return {
            'user_id': self.user_ids[idx],
            'item_id': self.item_ids[idx],
            'label': self.labels[idx]
        }

# 原始Embedding加载器
class OriginalEmbeddingLoader:
    def __init__(
        self,
        data_dir,
        item_meta_path,
        item_id_col,
        title_col,
        device="cpu"
    ):
        self.device = device
        self.data_dir = data_dir

        # 加载ID映射
        self.load_id_mappings()

        # 加载特征嵌入
        self.load_feature_embeddings()

        # 加载图嵌入
        self.load_graph_embeddings()

        # 加载文本嵌入
        self.load_text_embeddings()

        # 加载物品元数据
        meta = pd.read_csv(item_meta_path)[[item_id_col, title_col]].dropna()
        self.item_title_map = dict(zip(meta[item_id_col], meta[title_col]))

        # 初始化文本QFormer
        self.text_qformer = TextQFormer(768, 768, 32, 8).to(device).eval()

        logger.info(f"Loaded user feature embeddings: {self.user_feature_embeddings.weight.shape}")
        logger.info(f"Loaded item feature embeddings: {self.item_feature_embeddings.weight.shape}")

    def load_id_mappings(self):
        """加载ID映射"""
        mapping_dir = os.path.join(self.data_dir, 'id_mappings')
        user_mapping_df = pd.read_csv(f"{mapping_dir}/user_id_to_embedding_index_map.csv")
        item_mapping_df = pd.read_csv(f"{mapping_dir}/item_id_to_embedding_index_map.csv")

        self.user_id_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['embedding_idx']))
        self.item_id_to_idx = dict(zip(item_mapping_df['movie_id'], item_mapping_df['embedding_idx']))

        logger.info(f"Loaded ID mappings: {len(self.user_id_to_idx)} users, {len(self.item_id_to_idx)} items")

    def load_feature_embeddings(self):
        """加载特征嵌入"""
        user_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/user_embedder.pt')
        item_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/item_embedder.pt')

        # 加载预训练的特征嵌入器
        user_embedder = FeatureEmbedder.load_pretrained(model_path=user_feature_emb_path, device=self.device)
        item_embedder = FeatureEmbedder.load_pretrained(model_path=item_feature_emb_path, device=self.device)

        # 获取训练数据以处理特征
        train_path = os.path.join(self.data_dir, "train.csv")
        train_data = pd.read_csv(train_path)

        # 获取最大genres数量
        max_genres = 0
        for genres in train_data['genres_index'].apply(ast.literal_eval):
            if genres:
                max_genres = max(max_genres, len(genres))

        # 处理用户和物品数据
        user_data = train_data[['user_id', 'gender', 'age', 'occupation', 'zip_code_index']].drop_duplicates()
        user_data = user_data.sort_values('user_id').reset_index(drop=True)
        item_data = train_data[['movie_id', 'genres_index']].drop_duplicates()
        item_data = item_data.sort_values('movie_id').reset_index(drop=True)

        # 处理特征
        user_features = process_features(user_data, type='user')
        item_features = process_features(item_data, type='item', max_genres=max_genres)

        # 获取嵌入
        user_embeddings = user_embedder(user_features)
        item_embeddings = item_embedder(item_features)

        # 创建嵌入层
        self.user_feature_embeddings = torch.nn.Embedding.from_pretrained(user_embeddings, freeze=True).to(self.device)
        self.item_feature_embeddings = torch.nn.Embedding.from_pretrained(item_embeddings, freeze=True).to(self.device)

    def load_graph_embeddings(self):
        """加载图嵌入"""
        # 加载图嵌入路径
        graph_emb_dir = os.path.join(self.data_dir, 'graph_embeddings')
        user_item_user_graph_emb_path = os.path.join(graph_emb_dir, 'user_graph_embeddings.pt')
        user_item_item_graph_emb_path = os.path.join(graph_emb_dir, 'item_graph_embeddings.pt')
        user_user_click_graph_emb_path = os.path.join(graph_emb_dir, 'user_co_click_user_graph_embeddings.pt')
        user_user_imp_graph_emb_path = os.path.join(graph_emb_dir, 'user_co_impression_user_graph_embeddings.pt')
        item_item_click_graph_emb_path = os.path.join(graph_emb_dir, 'item_co_clicked_item_graph_embeddings.pt')
        item_item_imp_graph_emb_path = os.path.join(graph_emb_dir, 'item_co_impressed_item_graph_embeddings.pt')

        # 加载图嵌入
        self.user_item_user_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_user_graph_emb_path, map_location=self.device), freeze=True).to(self.device)
        self.user_item_item_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_item_graph_emb_path, map_location=self.device), freeze=True).to(self.device)
        self.user_user_click_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_click_graph_emb_path, map_location=self.device), freeze=True).to(self.device)
        self.user_user_impression_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_imp_graph_emb_path, map_location=self.device), freeze=True).to(self.device)
        self.item_item_clicked_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_click_graph_emb_path, map_location=self.device), freeze=True).to(self.device)
        self.item_item_impressed_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_imp_graph_emb_path, map_location=self.device), freeze=True).to(self.device)

        # 加载图ID映射
        self.load_graph_id_mappings()

    def load_graph_id_mappings(self):
        """加载图ID映射"""
        graph_id_mapping_dir = os.path.join(self.data_dir, 'graph_embeddings/mappings')
        item_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_id_to_embedding_idx.csv")
        user_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_id_to_embedding_idx.csv")
        user_user_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_click_user_id_mapping.csv")
        user_user_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_impression_user_id_mapping.csv")
        item_item_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_clicked_item_id_mapping.csv")
        item_item_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_impressed_item_id_mapping.csv")

        self.user_item_user_id2idx = dict(zip(user_id2emb['user_id'], user_id2emb['embedding_idx']))
        self.user_item_item_id2idx = dict(zip(item_id2emb['item_id'], item_id2emb['embedding_idx']))
        self.user_user_click_id2idx = dict(zip(user_user_click2emb['user_id'], user_user_click2emb['embedding_idx']))
        self.user_user_imp_id2idx = dict(zip(user_user_imp2emb['user_id'], user_user_imp2emb['embedding_idx']))
        self.item_item_click_id2idx = dict(zip(item_item_click2emb['item_id'], item_item_click2emb['embedding_idx']))
        self.item_item_imp_id2idx = dict(zip(item_item_imp2emb['item_id'], item_item_imp2emb['embedding_idx']))

    def load_text_embeddings(self):
        """加载文本嵌入"""
        text_emb_dir = os.path.join(self.data_dir, 'text_embeddings')
        user_text_emb_path = os.path.join(text_emb_dir, 'user_text_embeddings.pt')
        item_text_emb_path = os.path.join(text_emb_dir, 'item_text_embeddings.pt')

        self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
        self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)

    def map_ids_to_indices(self, user_ids, item_ids):
        """将ID映射到索引"""
        user_ids_list = user_ids.cpu().tolist()
        item_ids_list = item_ids.cpu().tolist()

        user_indices = torch.tensor([self.user_id_to_idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
        item_indices = torch.tensor([self.item_id_to_idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()

        return user_indices, item_indices

    def map_graph_ids_to_indices(self, user_ids, item_ids, graph_type):
        """将ID映射到图索引"""
        user_ids_list = user_ids.cpu().tolist()
        item_ids_list = item_ids.cpu().tolist()

        if graph_type == 'user-item-user':
            user_indices = torch.tensor([self.user_item_user_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'user-item-item':
            item_indices = torch.tensor([self.user_item_item_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        elif graph_type == 'user-user-click':
            user_indices = torch.tensor([self.user_user_click_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'user-user-impression':
            user_indices = torch.tensor([self.user_user_imp_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'item-item-click':
            item_indices = torch.tensor([self.item_item_click_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        elif graph_type == 'item-item-impression':
            item_indices = torch.tensor([self.item_item_imp_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        else:
            raise ValueError("Invalid graph type")

    def get_user_hgt_input(self, user_ids):
        """获取用户HGT输入特征"""
        user_indices, _ = self.map_ids_to_indices(user_ids, torch.zeros_like(user_ids))
        return self.user_feature_embeddings(user_indices)

    def get_item_hgt_input(self, item_ids):
        """获取物品HGT输入特征，并拼接文本特征"""
        _, item_indices = self.map_ids_to_indices(torch.zeros_like(item_ids), item_ids)
        item_feature_emb = self.item_feature_embeddings(item_indices)

        # 获取文本嵌入
        item_text_emb = self.item_text_embeddings[item_indices]

        # 拼接特征嵌入和文本嵌入
        return torch.cat([item_feature_emb, item_text_emb], dim=-1)

    def get_user_rgcn_input(self, user_ids):
        """获取用户RGCN输入特征"""
        # 获取用户-用户图嵌入
        user_click_indices = self.map_graph_ids_to_indices(user_ids, torch.zeros_like(user_ids), 'user-user-click')
        user_imp_indices = self.map_graph_ids_to_indices(user_ids, torch.zeros_like(user_ids), 'user-user-impression')

        user_click_emb = self.user_user_click_graph_embeddings(user_click_indices)
        user_imp_emb = self.user_user_impression_graph_embeddings(user_imp_indices)

        # 平均两种嵌入
        return (user_click_emb + user_imp_emb) / 2

    def get_item_rgcn_input(self, item_ids):
        """获取物品RGCN输入特征"""
        # 获取物品-物品图嵌入
        item_click_indices = self.map_graph_ids_to_indices(torch.zeros_like(item_ids), item_ids, 'item-item-click')
        item_imp_indices = self.map_graph_ids_to_indices(torch.zeros_like(item_ids), item_ids, 'item-item-impression')

        item_click_emb = self.item_item_clicked_graph_embeddings(item_click_indices)
        item_imp_emb = self.item_item_impressed_graph_embeddings(item_imp_indices)

        # 平均两种嵌入
        return (item_click_emb + item_imp_emb) / 2

# RoBERTa文本编码器
class RobertaTextEncoder(nn.Module):
    def __init__(self, tokenizer, model, device="cpu"):
        super().__init__()
        self.tokenizer = tokenizer
        self.model = model
        self.device = device
        self.model.eval()

    def forward(self, texts):
        tokens = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=128,
            return_tensors="pt"
        ).to(self.device)

        with torch.no_grad():
            outputs = self.model(**tokens)
            mask = tokens['attention_mask'].unsqueeze(-1)
            embeddings = (outputs.last_hidden_state * mask).sum(dim=1) / mask.sum(dim=1)
        return embeddings

# 构建HGT输入
def build_hgt_input(user_ids, item_ids, loader):
    device = loader.device
    x_dict = {}
    node_type = {}
    global_idx = 0

    uid_to_gid = {}
    iid_to_gid = {}

    # 获取用户特征
    user_feats = loader.get_user_hgt_input(user_ids).to(device).float()

    # 如果是3D张量，取第一个token的embedding
    if len(user_feats.shape) == 3:
        user_feats = user_feats[:, 0, :]

    x_dict[0] = user_feats
    for uid in user_ids:
        uid = int(uid)
        node_type[global_idx] = 0
        uid_to_gid[uid] = global_idx
        global_idx += 1

    # 获取物品特征
    item_feats = loader.get_item_hgt_input(item_ids).to(device).float()

    # 如果是3D张量，取第一个token的embedding
    if len(item_feats.shape) == 3:
        item_feats = item_feats[:, 0, :]

    x_dict[1] = item_feats
    for iid in item_ids:
        iid = int(iid)
        node_type[global_idx] = 1
        iid_to_gid[iid] = global_idx
        global_idx += 1

    edge_index = []
    edge_type = []

    # 创建稀疏连接模式以减少内存使用
    for uid in user_ids:
        for j, iid in enumerate(item_ids):
            if j % 10 == 0:  # 每10个物品连接一次，减少边数量
                u_idx = uid_to_gid[int(uid)]
                i_idx = iid_to_gid[int(iid)]
                edge_index.append([u_idx, i_idx])
                edge_type.append(0)  # user -> item (使用单一类型)

    # 转换为张量并移动到正确的设备
    edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
    edge_type = torch.LongTensor(edge_type).to(device)
    node_type_tensor = torch.tensor([node_type[i] for i in range(len(node_type))], dtype=torch.long).to(device)

    # 检查边索引是否有效
    max_node_idx = global_idx - 1
    max_edge_idx = edge_index.max().item() if edge_index.numel() > 0 else -1

    if max_edge_idx > max_node_idx:
        print(f"WARNING: Edge index {max_edge_idx} exceeds max node index {max_node_idx}")
        # 限制边索引到有效范围
        edge_index = torch.clamp(edge_index, 0, max_node_idx)

    return x_dict, edge_index, edge_type, node_type_tensor

# 构建RGCN输入
def build_rgcn_input(user_ids, item_ids, loader):
    device = loader.device

    # 获取用户和物品嵌入
    user_x = loader.get_user_rgcn_input(user_ids).to(device).float()
    item_x = loader.get_item_rgcn_input(item_ids).to(device).float()

    # 如果需要，重塑为2D
    if len(user_x.shape) == 3:
        user_x = user_x[:, 0, :]

    if len(item_x.shape) == 3:
        item_x = item_x[:, 0, :]

    x = torch.cat([user_x, item_x], dim=0)

    num_users = len(user_ids)

    edge_index = []
    edge_type = []

    # 创建稀疏连接模式
    for i, uid in enumerate(user_ids):
        for j, iid in enumerate(item_ids):
            if j % 10 == 0:  # 每10个物品连接一次
                edge_index.append([i, num_users + j])
                edge_type.append(0)  # 关系类型 0：点击

    edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
    edge_type = torch.LongTensor(edge_type).to(device)

    return x, edge_index, edge_type

# 评估函数
def evaluate(model, data_loader, loader, hgt, rgcn, device):
    model.eval()
    hgt.eval()
    rgcn.eval()

    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Evaluating"):
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            # 获取HGT嵌入
            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            # 获取RGCN嵌入
            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            # 提取用户和物品嵌入
            hgt_user_feat = hgt_out[:len(user_ids)]
            hgt_item_feat = hgt_out[len(user_ids):]
            rgcn_user_feat = rgcn_out[:len(user_ids)]
            rgcn_item_feat = rgcn_out[len(user_ids):]

            # 组合HGT和RGCN嵌入
            user_feat = hgt_user_feat + rgcn_user_feat
            item_feat = hgt_item_feat + rgcn_item_feat

            # 前向传播
            prob, _, _ = model(user_feat, item_feat)

            all_preds.extend(prob.squeeze().cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # 计算指标
    preds_np = np.array(all_preds)
    labels_np = np.array(all_labels)

    auc = roc_auc_score(labels_np, preds_np)
    logloss = log_loss(labels_np, preds_np)
    accuracy = accuracy_score(labels_np > 0.5, preds_np > 0.5)

    return {
        "auc": auc,
        "logloss": logloss,
        "accuracy": accuracy
    }

# 训练和评估函数
def train_and_evaluate(
    train_path,
    val_path,
    test_path,
    data_dir,
    item_meta_path,
    embedding_size,
    device,
    epochs,
    batch_size,
    lr,
    weight_decay,
    early_stop_patience,
    warmup_steps,
    max_grad_norm,
    seed,
    log_dir,
    use_wandb=False,
    run_name=None
):
    set_seed(seed)

    if run_name is None:
        run_name = f"embed_{embedding_size}_original"

    # 初始化wandb
    if use_wandb:
        wandb.init(
            project="GraphLLM4CTR_Original_Embeddings",
            name=run_name,
            config={
                "embedding_size": embedding_size,
                "batch_size": batch_size,
                "learning_rate": lr,
                "epochs": epochs,
                "weight_decay": weight_decay,
                "early_stop_patience": early_stop_patience,
                "warmup_steps": warmup_steps,
                "max_grad_norm": max_grad_norm,
                "seed": seed
            }
        )

    # 创建日志目录
    embed_log_dir = os.path.join(log_dir, run_name)
    os.makedirs(embed_log_dir, exist_ok=True)

    # 初始化embedding loader
    loader = OriginalEmbeddingLoader(
        data_dir=data_dir,
        item_meta_path=item_meta_path,
        item_id_col="movie_id",
        title_col="title",
        device=device
    )

    # 加载和预处理数据
    train_df = pd.read_csv(train_path)
    train_df = train_df.sample(frac=1, random_state=seed).reset_index(drop=True)
    val_df = pd.read_csv(val_path)
    test_df = pd.read_csv(test_path)

    train_loader = DataLoader(CTRDataset(train_df), batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(CTRDataset(val_df), batch_size=batch_size)
    test_loader = DataLoader(CTRDataset(test_df), batch_size=batch_size)

    # 计算expert输出维度为embedding size的一半
    expert_output_dim = max(32, embedding_size // 2)

    # 初始化模型
    # 检查HGT输入维度
    # 获取一个样本来确定维度
    sample_user_ids = torch.tensor([train_df['user_id'].iloc[0]], device=device)
    sample_item_ids = torch.tensor([train_df['movie_id'].iloc[0]], device=device)
    sample_user_emb = loader.get_user_hgt_input(sample_user_ids)
    sample_item_emb = loader.get_item_hgt_input(sample_item_ids)

    # 检查维度是否匹配预期
    user_dim = sample_user_emb.shape[-1]
    item_dim = sample_item_emb.shape[-1]

    logger.info(f"HGT input dimensions: user={user_dim}, item={item_dim}")

    # 创建一个简单的特征投影层，将不同维度的特征投影到相同的维度
    class FeatureProjector(nn.Module):
        def __init__(self, input_dims, output_dim):
            super().__init__()
            self.projectors = nn.ModuleDict()
            for node_type, dim in input_dims.items():
                self.projectors[str(node_type)] = nn.Linear(dim, output_dim)

        def forward(self, x_dict):
            projected_x = {}
            for node_type, x in x_dict.items():
                projected_x[node_type] = self.projectors[str(node_type)](x)
            return projected_x

    # 定义投影后的维度（使用embedding_size）
    projected_dim = embedding_size

    # 创建特征投影器
    feature_projector = FeatureProjector(
        input_dims={0: user_dim, 1: item_dim},
        output_dim=projected_dim
    ).to(device)

    # HGT - 使用投影后的维度
    hgt = ResLLMHGTEncoder(
        input_dim=projected_dim,
        hidden_dim=embedding_size,
        num_layers=2,
        num_types=2,
        num_heads=min(4, max(1, embedding_size // 64)),
        dropout=0.1,
        device=device
    ).to(device)

    # 检查RGCN输入维度
    # 使用相同的样本ID
    sample_user_emb_rgcn = loader.get_user_rgcn_input(sample_user_ids)
    sample_item_emb_rgcn = loader.get_item_rgcn_input(sample_item_ids)
    rgcn_input_dim = sample_user_emb_rgcn.shape[-1]  # 获取实际维度

    logger.info(f"RGCN input dimension: {rgcn_input_dim}")

    # R-GCN - 使用检测到的输入维度
    rgcn = ResLLMRGCNEncoder(
        input_dim=rgcn_input_dim,
        hidden_dim=embedding_size,
        num_relations=2,
        num_layers=2,
        dropout=0.1,
        device=device
    ).to(device)

    # Expert fusion模型
    model = FocusedHybridExpertAdaptor(
        input_dim=embedding_size,
        expert_output_dim=expert_output_dim,
        num_shared_experts=3,
        num_user_experts=3,
        num_item_experts=3,
        hidden_dim=expert_output_dim,
        dropout=0.1
    ).to(device)

    # 组合所有参数进行优化
    all_params = list(model.parameters()) + list(hgt.parameters()) + list(rgcn.parameters())

    # 优化器
    optimizer = torch.optim.AdamW(
        all_params,
        lr=lr,
        weight_decay=weight_decay
    )

    # 学习率调度器
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )

    # 损失函数
    criterion = nn.BCELoss()

    # 初始化跟踪变量
    best_auc = 0
    best_accuracy = 0
    patience_counter = 0

    # 跟踪指标
    epoch_metrics = {
        "train_loss": [],
        "val_auc": [],
        "val_logloss": [],
        "val_accuracy": [],
        "learning_rates": []
    }

    # 跟踪训练时间
    start_time = time.time()

    # 训练循环
    for epoch in range(epochs):
        model.train()
        hgt.train()
        rgcn.train()

        total_loss = 0
        batch_losses = []

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")):
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            # 获取HGT嵌入
            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            # 获取RGCN嵌入
            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            # 提取用户和物品嵌入
            hgt_user_feat = hgt_out[:len(user_ids)]
            hgt_item_feat = hgt_out[len(user_ids):]
            rgcn_user_feat = rgcn_out[:len(user_ids)]
            rgcn_item_feat = rgcn_out[len(user_ids):]

            # 组合HGT和RGCN嵌入
            user_feat = hgt_user_feat + rgcn_user_feat
            item_feat = hgt_item_feat + rgcn_item_feat

            # 前向传播
            prob, _, _ = model(user_feat, item_feat)

            # 计算损失
            loss = criterion(prob.squeeze(), labels)

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(all_params, max_grad_norm)

            optimizer.step()
            scheduler.step()

            # 跟踪损失
            batch_loss = loss.item()
            total_loss += batch_loss
            batch_losses.append(batch_loss)

            # 每10个batch记录到wandb
            if use_wandb and batch_idx % 10 == 0:
                wandb.log({
                    "batch": epoch * len(train_loader) + batch_idx,
                    "batch_loss": batch_loss,
                    "running_avg_loss": total_loss / (batch_idx + 1),
                    "learning_rate": scheduler.get_last_lr()[0]
                })

        # 在验证集上评估
        val_metrics = evaluate(model, val_loader, loader, hgt, rgcn, device)

        # 计算epoch指标
        avg_epoch_loss = total_loss / len(train_loader)
        epoch_loss_std = np.std(batch_losses) if len(batch_losses) > 1 else 0

        # 跟踪指标
        epoch_metrics["train_loss"].append(avg_epoch_loss)
        epoch_metrics["val_auc"].append(val_metrics["auc"])
        epoch_metrics["val_logloss"].append(val_metrics["logloss"])
        epoch_metrics["val_accuracy"].append(val_metrics["accuracy"])
        epoch_metrics["learning_rates"].append(scheduler.get_last_lr()[0])

        # 记录指标
        if use_wandb:
            wandb.log({
                "epoch": epoch+1,
                "train_loss": avg_epoch_loss,
                "train_loss_std": epoch_loss_std,
                "val_auc": val_metrics["auc"],
                "val_logloss": val_metrics["logloss"],
                "val_accuracy": val_metrics["accuracy"]
            })

        print(f"[Epoch {epoch+1}/{epochs}] Train Loss: {avg_epoch_loss:.4f} (±{epoch_loss_std:.4f}) | "
              f"Val AUC: {val_metrics['auc']:.4f} | Val Accuracy: {val_metrics['accuracy']:.4f}")

        # 基于AUC保存最佳模型
        if val_metrics["auc"] > best_auc:
            best_auc = val_metrics["auc"]
            best_accuracy = val_metrics["accuracy"]
            patience_counter = 0

            # 保存所有模型组件
            torch.save({
                'model': model.state_dict(),
                'hgt': hgt.state_dict(),
                'rgcn': rgcn.state_dict(),
                'epoch': epoch,
                'val_auc': val_metrics["auc"],
                'val_accuracy': val_metrics["accuracy"]
            }, os.path.join(embed_log_dir, "best_model.pt"))

            print(f"✓ 保存新的最佳模型! AUC: {best_auc:.4f}, Accuracy: {best_accuracy:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= early_stop_patience:
                print(f"在epoch {epoch+1}提前停止")
                break

    # 计算总训练时间
    training_time = time.time() - start_time

    # 加载最佳模型进行测试
    checkpoint = torch.load(os.path.join(embed_log_dir, "best_model.pt"))
    model.load_state_dict(checkpoint['model'])
    hgt.load_state_dict(checkpoint['hgt'])
    rgcn.load_state_dict(checkpoint['rgcn'])

    # 测试模型
    test_metrics = evaluate(model, test_loader, loader, hgt, rgcn, device)
    print(f"\n[测试结果] AUC: {test_metrics['auc']:.4f} | Accuracy: {test_metrics['accuracy']:.4f} | LogLoss: {test_metrics['logloss']:.4f}")

    if use_wandb:
        wandb.log({
            "test_auc": test_metrics["auc"],
            "test_logloss": test_metrics["logloss"],
            "test_accuracy": test_metrics["accuracy"],
            "best_epoch": checkpoint['epoch'],
            "best_val_auc": checkpoint['val_auc'],
            "training_time": training_time,
            "model_size": sum(p.numel() for p in all_params)
        })
        wandb.finish()

    # 保存指标进行分析
    results = {
        "embedding_size": embedding_size,
        "expert_output_dim": expert_output_dim,
        "best_val_auc": best_auc,
        "best_val_accuracy": best_accuracy,
        "test_auc": test_metrics["auc"],
        "test_accuracy": test_metrics["accuracy"],
        "test_logloss": test_metrics["logloss"],
        "best_epoch": checkpoint['epoch'],
        "epoch_metrics": epoch_metrics,
        "early_stopped": patience_counter >= early_stop_patience,
        "training_time": training_time,
        "model_size": sum(p.numel() for p in all_params)
    }

    # 保存结果到JSON
    with open(os.path.join(embed_log_dir, "results.json"), 'w') as f:
        json.dump(results, f, indent=4)

    return results


def run_embedding_size_analysis(args):
    # 创建主日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = os.path.join(args.log_dir, f"original_embedding_analysis_{timestamp}")
    os.makedirs(log_dir, exist_ok=True)

    # 只使用512作为embedding size
    embedding_sizes = [512]

    # 存储所有embedding size的结果
    all_results = []

    # 为每个embedding size运行分析
    for embed_size in embedding_sizes:
        print(f"\n{'='*50}")
        print(f"分析Embedding Size: {embed_size}")
        print(f"{'='*50}\n")

        results = train_and_evaluate(
            train_path=args.train_path,
            val_path=args.val_path,
            test_path=args.test_path,
            data_dir=args.data_dir,
            item_meta_path=args.item_meta_path,
            embedding_size=embed_size,
            device=args.device,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr,
            weight_decay=args.weight_decay,
            early_stop_patience=args.early_stop_patience,
            warmup_steps=args.warmup_steps,
            max_grad_norm=args.max_grad_norm,
            seed=args.seed,
            log_dir=log_dir,
            use_wandb=args.use_wandb
        )

        all_results.append(results)

    # 保存所有结果到单个文件
    with open(os.path.join(log_dir, "all_results.json"), 'w') as f:
        json.dump(all_results, f, indent=4)

    return all_results


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--train_path", type=str, default='/data/datasets/processed_datasets/movielens/train.csv')
    parser.add_argument("--val_path", type=str, default='/data/datasets/processed_datasets/movielens/val.csv')
    parser.add_argument("--test_path", type=str, default='/data/datasets/processed_datasets/movielens/test.csv')
    parser.add_argument("--data_dir", type=str, default='/data/datasets/processed_datasets/movielens', help="Path to the dataset directory containing embeddings")
    parser.add_argument("--item_meta_path", type=str, default='/data/datasets/processed_datasets/movielens/train.csv')
    parser.add_argument("--epochs", type=int, default=15)
    parser.add_argument("--batch_size", type=int, default=256)
    parser.add_argument("--lr", type=float, default=0.002)
    parser.add_argument("--weight_decay", type=float, default=1e-5)
    parser.add_argument("--early_stop_patience", type=int, default=5)
    parser.add_argument("--warmup_steps", type=int, default=500)
    parser.add_argument("--max_grad_norm", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--log_dir", type=str, default='/data/datasets/processed_datasets/movielens/embedding_analysis/original')
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--use_wandb", action="store_true")
    args = parser.parse_args()

    # 运行embedding size分析
    os.makedirs(args.log_dir, exist_ok=True)
    results = run_embedding_size_analysis(args)
