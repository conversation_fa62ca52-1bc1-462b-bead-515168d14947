#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'  # 优化CUDA内存分配

import torch
import logging
import argparse
import pandas as pd
from tqdm import tqdm
from datetime import datetime
from torch.utils.data import DataLoader, Dataset
from torch.cuda.amp import autocast, GradScaler  # 添加混合精度训练
from .ablation_contrastive_model import AblationContrastiveLearner
# 使用原始路径导入
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'models'))
from models.optimized_batch_builder_for_check import OptimizedBatchBuilder  # 使用优化的版本
from collections import defaultdict
from transformers import get_constant_schedule_with_warmup

import wandb
wandb.login(anonymous="allow")

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmbeddingCollector:
    """Collect embeddings during training to save aligned representations"""
    def __init__(self):
        self.user_embeddings = defaultdict(dict)
        self.item_embeddings = defaultdict(dict)

    def add_batch(self, user_ids, item_ids, outputs):
        """Add batch outputs to the collector"""
        # Handle both tensor and list inputs
        if isinstance(user_ids, torch.Tensor):
            user_ids_list = user_ids.cpu().tolist()
        else:
            user_ids_list = user_ids

        if isinstance(item_ids, torch.Tensor):
            item_ids_list = item_ids.cpu().tolist()
        else:
            item_ids_list = item_ids

        # Collect user embeddings
        for idx, user_id in enumerate(user_ids_list):
            # Graph projections
            if 'user_graph_proj' in outputs:
                self.user_embeddings['graph_proj'][user_id] = outputs['user_graph_proj'][idx].detach().cpu()
            if 'user_click_graph_proj' in outputs:
                self.user_embeddings['click_proj'][user_id] = outputs['user_click_graph_proj'][idx].detach().cpu()
            if 'user_imp_graph_proj' in outputs:
                self.user_embeddings['impression_proj'][user_id] = outputs['user_imp_graph_proj'][idx].detach().cpu()

            # Text projections
            if 'user_text_proj' in outputs:
                self.user_embeddings['text_proj'][user_id] = outputs['user_text_proj'][idx].detach().cpu()

        # Collect item embeddings
        for idx, item_id in enumerate(item_ids_list):
            # Graph projections
            if 'item_graph_proj' in outputs:
                self.item_embeddings['graph_proj'][item_id] = outputs['item_graph_proj'][idx].detach().cpu()
            if 'item_click_graph_proj' in outputs:
                self.item_embeddings['clicked_proj'][item_id] = outputs['item_click_graph_proj'][idx].detach().cpu()
            if 'item_imp_graph_proj' in outputs:
                self.item_embeddings['impressed_proj'][item_id] = outputs['item_imp_graph_proj'][idx].detach().cpu()

            # Text projections
            if 'item_text_proj' in outputs:
                self.item_embeddings['text_proj'][item_id] = outputs['item_text_proj'][idx].detach().cpu()

    def save_embeddings(self, save_dir, epoch):
        """Save all collected embeddings with ID mappings"""
        epoch_dir = os.path.join(save_dir, f"aligned_embeddings_epoch{epoch}")
        os.makedirs(epoch_dir, exist_ok=True)

        # Save user embeddings
        for emb_type, emb_dict in self.user_embeddings.items():
            if emb_dict:
                user_ids = sorted(list(emb_dict.keys()))
                embeddings = torch.stack([emb_dict[uid] for uid in user_ids])

                # Save embeddings
                torch.save(embeddings, os.path.join(epoch_dir, f"user_{emb_type}.pt"))

                # Save ID mapping
                id_mapping = pd.DataFrame({'user_id': user_ids, 'embedding_idx': range(len(user_ids))})
                id_mapping.to_csv(os.path.join(epoch_dir, f"user_{emb_type}_id_mapping.csv"), index=False)

        # Save item embeddings
        for emb_type, emb_dict in self.item_embeddings.items():
            if emb_dict:
                item_ids = sorted(list(emb_dict.keys()))
                embeddings = torch.stack([emb_dict[iid] for iid in item_ids])

                # Save embeddings
                torch.save(embeddings, os.path.join(epoch_dir, f"item_{emb_type}.pt"))

                # Save ID mapping
                id_mapping = pd.DataFrame({'item_id': item_ids, 'embedding_idx': range(len(item_ids))})
                id_mapping.to_csv(os.path.join(epoch_dir, f"item_{emb_type}_id_mapping.csv"), index=False)

    def clear(self):
        """Clear the collector for next epoch"""
        self.user_embeddings.clear()
        self.item_embeddings.clear()

class ContrastiveDataset(Dataset):
    def __init__(self, data_path, dataset_type='movielens'):
        self.data = pd.read_csv(data_path)
        self.dataset_type = dataset_type

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        # Handle different column names for different datasets
        if self.dataset_type == 'movielens':
            item_column = 'movie_id'
        else:  # bookcrossing
            item_column = 'item_id'

        return {
            'user_id': row['user_id'],
            'item_id': row[item_column],
            'label': row['label']
        }

def train_ablation_contrastive(
    data_dir,
    model_save_dir,
    batch_size,
    num_epochs,
    learning_rate,
    temperature,
    device,
    ablation_type,
    wandb_project=None,
    dataset_type='movielens'
):
    # Create model save directory with ablation type
    model_save_dir = os.path.join(model_save_dir, ablation_type)
    os.makedirs(model_save_dir, exist_ok=True)

    # Set up file handler for logging
    log_file = os.path.join(model_save_dir, 'contrastive_learning.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)

    logger.info(f"Logging to {log_file}")
    logger.info(f"Ablation type: {ablation_type}")

    # Initialize wandb if project name is provided
    timestamp = datetime.now().strftime('%Y%m%d_%H%M')
    if wandb_project:
        wandb.init(
            project=wandb_project,
            name=f"{ablation_type}_{timestamp}",
            group=ablation_type,
            config={
                "learning_rate": learning_rate,
                "batch_size": batch_size,
                "epochs": num_epochs,
                "model": "AblationContrastiveLearner",
                "ablation_type": ablation_type,
                "device": device,
                "temperature": temperature
            }
        )

    logger.info("Loading dataset and dataloader")
    train_dataset = ContrastiveDataset(os.path.join(data_dir, "train.csv"), dataset_type=dataset_type)

    # 优化的DataLoader配置
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=8,  # 增加worker数量
        pin_memory=True,  # 启用pin_memory加速GPU传输
        persistent_workers=True,  # 保持worker进程
        prefetch_factor=2  # 预取因子
    )

    logger.info("Initializing batch builder")
    batch_builder = OptimizedBatchBuilder(data_dir, batch_size, device, dataset_type=dataset_type)

    logger.info(f"Initializing contrastive learning model with ablation: {ablation_type}")
    model = AblationContrastiveLearner(
        graph_hidden_dim=96,
        text_hidden_dim=768,
        common_dim=256,
        num_queries=32,
        temperature=temperature,
        structure_weight=0.7,
        strong_neg_weight=1.0,
        weak_neg_weight=0.3,
        num_heads=8,
        ablation_type=ablation_type
    ).to(device)

    params = list(model.parameters()) + \
             list(batch_builder.user_feature_embeddings.parameters()) + \
             list(batch_builder.item_feature_embeddings.parameters()) + \
             list(batch_builder.user_item_user_graph_embeddings.parameters()) + \
             list(batch_builder.user_item_item_graph_embeddings.parameters()) + \
             list(batch_builder.user_user_click_graph_embeddings.parameters()) + \
             list(batch_builder.user_user_impression_graph_embeddings.parameters()) + \
             list(batch_builder.item_item_clicked_graph_embeddings.parameters()) + \
             list(batch_builder.item_item_impressed_graph_embeddings.parameters())

    optimizer = torch.optim.Adam(params, lr=learning_rate)

    # 初始化混合精度训练
    scaler = GradScaler()

    logger.info("Starting training")

    clip_value = 1.0
    total_steps = len(train_loader) * num_epochs
    warmup_steps = int(0.2 * total_steps)
    scheduler = get_constant_schedule_with_warmup(optimizer, num_warmup_steps=warmup_steps)
    best_loss = float('inf')
    patience = 3
    patience_counter = 0

    for epoch in tqdm(range(num_epochs)):
        model.train()
        total_loss = 0
        loss_components = {
            'user_item_loss': 0,
            'user_structure_loss': 0,
            'item_structure_loss': 0,
            'user_text_loss': 0,
            'item_text_loss': 0
        }

        # Embedding collector for this epoch
        epoch_embedding_collector = EmbeddingCollector()

        # Track batch-level metrics
        batch_count = 0

        for batch_idx, batch_data in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")):
            user_ids = batch_data['user_id']
            item_ids = batch_data['item_id']

            batch = batch_builder.build_batch(user_ids, item_ids, batch_data['label'])

            optimizer.zero_grad()

            # 使用自动混合精度
            with autocast():
                outputs = model(batch)
                loss = outputs['total_loss']

            # 使用梯度缩放
            scaler.scale(loss).backward()

            # 梯度裁剪（在unscale之后）
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(params, clip_value)

            # 优化器步骤
            scaler.step(optimizer)
            scheduler.step()
            scaler.update()

            total_loss += loss.item()

            for key in loss_components:
                # 检查输出是否已经是float，如果是tensor则调用.item()
                if isinstance(outputs[key], torch.Tensor):
                    loss_components[key] += outputs[key].item()
                else:
                    loss_components[key] += outputs[key]

            epoch_embedding_collector.add_batch(user_ids, item_ids, outputs)

            # 定期清理GPU缓存
            if batch_idx % 50 == 0:
                torch.cuda.empty_cache()

            # 监控GPU内存使用
            if batch_idx % 10 == 0:
                gpu_memory = torch.cuda.memory_allocated() / 1e9
                logger.info(f"Batch {batch_idx}/{len(train_loader)}, GPU Memory: {gpu_memory:.2f}GB")

            # Log batch-level metrics to wandb
            if wandb_project and batch_count % 10 == 0:  # Log every 10 batches
                batch_metrics = {
                    "batch/total_loss": loss.item(),
                    "batch/gpu_memory_gb": torch.cuda.memory_allocated() / 1e9,
                    "batch/learning_rate": optimizer.param_groups[0]['lr']
                }
                for key in loss_components:
                    if isinstance(outputs[key], torch.Tensor):
                        batch_metrics[f"batch/{key}"] = outputs[key].item()
                    else:
                        batch_metrics[f"batch/{key}"] = outputs[key]

                wandb.log(batch_metrics, step=epoch * len(train_loader) + batch_count)

            batch_count += 1

        avg_loss = total_loss / len(train_loader)

        # Log epoch-level metrics to wandb
        if wandb_project:
            epoch_metrics = {
                "epoch": epoch + 1,
                "epoch/total_loss": avg_loss,
                "learning_rate": scheduler.get_last_lr()[0],
                "epoch/gpu_memory_gb": torch.cuda.memory_allocated() / 1e9
            }

            # Add component losses
            for key, value in loss_components.items():
                epoch_metrics[f"epoch/{key}"] = value / len(train_loader)

            # Add temperature parameter
            epoch_metrics["temperature"] = model.contrastive_loss.temp.item()

            wandb.log(epoch_metrics)

        # Log to console
        logger.info(f"Epoch {epoch+1}/{num_epochs}")
        logger.info(f"Total Loss: {avg_loss:.4f}")
        for key, value in loss_components.items():
            logger.info(f"{key}: {value/len(train_loader):.4f}")

        logger.info(f"Epoch {epoch+1}/{num_epochs} - Loss: {avg_loss:.4f}")

        # save
        checkpoint = {
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'scaler_state_dict': scaler.state_dict()  # 保存scaler状态
        }

        torch.save(checkpoint, os.path.join(model_save_dir, f"qformer_epoch{epoch+1}.pt"))

        epoch_embedding_collector.save_embeddings(model_save_dir, epoch + 1)

        torch.save(batch_builder.user_feature_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"user_feat_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.item_feature_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"item_feat_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.user_item_user_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"user_item_user_graph_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.user_item_item_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"user_item_item_graph_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.user_user_click_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"user_user_click_graph_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.user_user_impression_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"user_user_impression_graph_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.item_item_clicked_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"item_item_clicked_graph_embed_epoch{epoch+1}.pt"))
        torch.save(batch_builder.item_item_impressed_graph_embeddings.state_dict(),
                   os.path.join(model_save_dir, f"item_item_impressed_graph_embed_epoch{epoch+1}.pt"))

        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            patience_counter = 0
            torch.save(model.state_dict(), os.path.join(model_save_dir, "best_model.pt"))
            logger.info(f"New best model saved with loss: {best_loss:.4f}")

            if wandb_project:
                wandb.run.summary["best_loss"] = best_loss
                wandb.run.summary["best_epoch"] = epoch + 1
        else:
            patience_counter += 1
            logger.info(f"No improvement. Patience counter: {patience_counter}/{patience}")
            if patience_counter >= patience:
                logger.info(f"Early stopping at epoch {epoch+1}")
                if wandb_project:
                    wandb.run.summary["early_stopped"] = True
                    wandb.run.summary["stopped_epoch"] = epoch + 1
                break

    logger.info("Training completed!")
    logger.info(f"Best loss: {best_loss:.4f}")

    # 清理GPU缓存
    torch.cuda.empty_cache()

    # Log final metrics
    if wandb_project:
        wandb.run.summary["final_loss"] = avg_loss
        wandb.run.summary["total_epochs"] = epoch + 1
        wandb.run.summary["final_gpu_memory_gb"] = torch.cuda.memory_allocated() / 1e9
        wandb.finish()

def monitor_gpu_performance():
    """监控GPU使用情况"""
    if torch.cuda.is_available():
        print(f"GPU Memory Allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB")
        print(f"GPU Memory Cached: {torch.cuda.memory_reserved() / 1e9:.2f} GB")
        print(f"GPU Utilization: {torch.cuda.utilization()}%")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_dir", type=str, required=True, help="Path to the dataset directory")
    parser.add_argument("--model_save_dir", type=str, default="./ablation_results", help="Path to save the trained model")
    parser.add_argument("--batch_size", type=int, default=128)
    parser.add_argument("--num_epochs", type=int, default=3)
    parser.add_argument("--learning_rate", type=float, default=5e-4)
    parser.add_argument("--temperature", type=float, default=0.05)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--wandb_project", type=str, default='GraphLLM4CTR_Ablation_Study', help="Weights & Biases project name (optional)")
    parser.add_argument("--ablation_type", type=str, default="none",
                        choices=["none", "wo_user_item", "wo_user_graph", "wo_item_graph",
                                "wo_user_item_and_item_graph", "wo_user_item_and_user_graph",
                                "wo_user_graph_and_item_graph"],
                        help="Type of ablation study to run")
    parser.add_argument("--dataset_type", type=str, default="movielens",
                        choices=["movielens", "bookcrossing"],
                        help="Dataset type (movielens or bookcrossing)")
    args = parser.parse_args()

    # 开始训练前打印GPU信息
    if args.device == "cuda":
        print(f"Using GPU: {torch.cuda.get_device_name(0)}")
        monitor_gpu_performance()

    train_ablation_contrastive(
        data_dir=args.data_dir,
        model_save_dir=args.model_save_dir,
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        learning_rate=args.learning_rate,
        temperature=args.temperature,
        device=args.device,
        ablation_type=args.ablation_type,
        wandb_project=args.wandb_project,
        dataset_type=args.dataset_type
    )
