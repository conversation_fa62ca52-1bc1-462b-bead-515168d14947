# 使用原始Embeddings训练CTR模型

这个脚本用于在不使用对比学习结果的情况下，直接使用原始embeddings训练CTR模型。这可以作为消融实验的基线，用于比较对比学习带来的改进。

## 文件说明

- `ctr_with_original_embeddings.py`: 主要的CTR训练脚本，使用原始embeddings而不是对比学习后的结果
- `run_ctr_with_original_embeddings.sh`: 运行脚本，设置了合适的参数

## 使用方法

1. 首先，确保您有包含所有必要嵌入和映射的数据目录。该目录应包含以下子目录：
   - `feature_embeddings`: 包含特征嵌入器
   - `graph_embeddings`: 包含图嵌入
   - `text_embeddings`: 包含文本嵌入
   - `id_mappings`: 包含ID映射

2. 修改`run_ctr_with_original_embeddings.sh`中的路径，指向您的数据目录：

```bash
# 设置数据目录
DATA_DIR="/path/to/your/data/directory"
```

3. 运行脚本：

```bash
./run_ctr_with_original_embeddings.sh
```

## 参数说明

脚本支持以下参数：

- `--train_path`: 训练数据路径
- `--val_path`: 验证数据路径
- `--test_path`: 测试数据路径
- `--data_dir`: 数据目录路径，包含所有必要的嵌入和映射
- `--item_meta_path`: 物品元数据路径
- `--epochs`: 训练轮数
- `--batch_size`: 批次大小
- `--lr`: 学习率
- `--weight_decay`: 权重衰减
- `--early_stop_patience`: 早停耐心值
- `--warmup_steps`: 预热步数
- `--max_grad_norm`: 最大梯度范数
- `--seed`: 随机种子
- `--log_dir`: 日志目录
- `--device`: 设备（cuda或cpu）
- `--use_wandb`: 是否使用Weights & Biases记录实验

## 与对比学习结果比较

运行此脚本后，您可以将结果与使用对比学习后embeddings训练的CTR模型进行比较。这将帮助您理解对比学习对CTR预测性能的影响。

比较指标包括：
- AUC
- LogLoss
- 准确率

## 注意事项

- 脚本会自动检测嵌入的维度并相应地配置模型
- 确保数据目录包含所有必要的嵌入和映射文件
- 如果嵌入文件不存在，您需要先运行特征提取、图嵌入和文本嵌入脚本生成它们
- 此脚本使用与`hyperparameter_analysis_embedding.py`相同的模型架构，但不依赖对比学习的结果
