import os
import torch
import pandas as pd
import numpy as np
import ast
from torch_geometric.utils import subgraph
from models.feature_embedding import Feature<PERSON>mbedder, process_features

class OptimizedBatchBuilder:
    def __init__(self, data_dir, batch_size, device):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.device = device
        
        self.load_id_mappings()
        self.load_embeddings()
        self.load_graphs()
        self.load_graph_id_mappings()
        self.load_graph_matrixs()

        self.user_edge_type1_index = torch.load("/root/code/GraphLLM4CTR/user_edge_type1_edge_index.pt")
        self.user_edge_type2_index = torch.load("/root/code/GraphLLM4CTR/user_edge_type2_edge_index.pt")
        self.item_edge_type1_index = torch.load("/root/code/GraphLLM4CTR/item_edge_type1_edge_index.pt")
        self.item_edge_type2_index = torch.load("/root/code/GraphLLM4CTR/item_edge_type2_edge_index.pt")

        self.num_users = 6025   
        self.num_items = 3561

    def load_graph_matrixs(self):
        """Load graph matrices and keep them on CPU to save GPU memory"""
        graph_matrix_dir = os.path.join(self.data_dir, 'graph')
        
        # Load matrices to CPU
        self.click_matrix = torch.load(f"{graph_matrix_dir}/click_matrix.pt", map_location='cpu')
        self.impression_matrix = torch.load(f"{graph_matrix_dir}/impression_matrix.pt", map_location='cpu')
        self.user_edge_type1 = torch.load(f"{graph_matrix_dir}/user_edge_type1.pt", map_location='cpu')
        self.user_edge_type2 = torch.load(f"{graph_matrix_dir}/user_edge_type2.pt", map_location='cpu')
        self.item_edge_type1 = torch.load(f"{graph_matrix_dir}/item_edge_type1.pt", map_location='cpu')
        self.item_edge_type2 = torch.load(f"{graph_matrix_dir}/item_edge_type2.pt", map_location='cpu')

        self.click_matrix_dense = self.click_matrix.to_dense()

        
        # Log matrix information
        # print(f"Loaded graph matrices on CPU:")
        # for name, matrix in [
        #     ('click_matrix', self.click_matrix),
        #     ('impression_matrix', self.impression_matrix),
        #     ('user_edge_type1', self.user_edge_type1),
        #     ('user_edge_type2', self.user_edge_type2),
        #     ('item_edge_type1', self.item_edge_type1),
        #     ('item_edge_type2', self.item_edge_type2)
        # ]:
        #     if isinstance(matrix, torch.sparse.Tensor):
        #         print(f"  {name}: sparse {matrix.shape}, nnz={matrix._nnz()}")
        #     else:
        #         print(f"  {name}: dense {matrix.shape}")

        # print("DEBUG click_matrix shape =", self.click_matrix.shape)

    def load_id_mappings(self):
        mapping_dir = os.path.join(self.data_dir, 'id_mappings')
        user_mapping_df = pd.read_csv(f"{mapping_dir}/user_id_to_embedding_index_map.csv")
        item_mapping_df = pd.read_csv(f"{mapping_dir}/item_id_to_embedding_index_map.csv")
        
        self.user_id_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['embedding_idx']))
        self.item_id_to_idx = dict(zip(item_mapping_df['movie_id'], item_mapping_df['embedding_idx']))

    def load_graph_id_mappings(self):
        graph_id_mapping_dir = os.path.join(self.data_dir, 'graph_embeddings/mappings')
        item_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_id_to_embedding_idx.csv")
        user_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_id_to_embedding_idx.csv")
        user_user_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_click_user_id_mapping.csv")
        user_user_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_impression_user_id_mapping.csv")
        item_item_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_clicked_item_id_mapping.csv")
        item_item_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_impressed_item_id_mapping.csv")

        self.user_item_user_id2idx = dict(zip(user_id2emb['user_id'], user_id2emb['embedding_idx']))
        self.user_item_item_id2idx = dict(zip(item_id2emb['item_id'], item_id2emb['embedding_idx']))
        self.user_user_click_id2idx = dict(zip(user_user_click2emb['user_id'], user_user_click2emb['embedding_idx']))
        self.user_user_imp_id2idx = dict(zip(user_user_imp2emb['user_id'], user_user_imp2emb['embedding_idx']))
        self.item_item_click_id2idx = dict(zip(item_item_click2emb['item_id'], item_item_click2emb['embedding_idx']))
        self.item_item_imp_id2idx = dict(zip(item_item_imp2emb['item_id'], item_item_imp2emb['embedding_idx']))
    
    def get_max_genres(self, item_data):
        max_genres = 0
        for genres in item_data['genres_index'].apply(ast.literal_eval):
            if genres:
                max_genres = max(max_genres, len(genres))
        return max_genres

    def load_embeddings(self):
        train_path = os.path.join(self.data_dir, "train.csv")
        train_data = pd.read_csv(train_path)
        max_genres = self.get_max_genres(train_data)
        
        # Load feature embeddings
        user_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/user_embedder.pt')
        item_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/item_embedder.pt')
        user_embedder = FeatureEmbedder.load_pretrained(model_path=user_feature_emb_path, device=self.device)
        item_embedder = FeatureEmbedder.load_pretrained(model_path=item_feature_emb_path, device=self.device)
        
        # Process user and item data
        user_data = train_data[['user_id', 'gender', 'age', 'occupation', 'zip_code_index']].drop_duplicates()
        user_data = user_data.sort_values('user_id').reset_index(drop=True)
        item_data = train_data[['movie_id', 'genres_index']].drop_duplicates()
        item_data = item_data.sort_values('movie_id').reset_index(drop=True)
        
        user_features = process_features(user_data, type='user')
        item_features = process_features(item_data, type='item', max_genres=max_genres)
        
        # Convert tensors to nn.Embedding
        user_embeddings = user_embedder(user_features)
        item_embeddings = item_embedder(item_features)
        self.user_feature_embeddings = torch.nn.Embedding.from_pretrained(user_embeddings, freeze=False).to(self.device)
        self.item_feature_embeddings = torch.nn.Embedding.from_pretrained(item_embeddings, freeze=False).to(self.device)

        # Load graph embeddings
        user_item_user_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_graph_embeddings.pt')
        user_item_item_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_graph_embeddings.pt')
        user_user_click_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_co_click_user_graph_embeddings.pt')
        user_user_imp_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_co_impression_user_graph_embeddings.pt')
        item_item_click_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_co_clicked_item_graph_embeddings.pt')
        item_item_imp_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_co_impressed_item_graph_embeddings.pt')

        # Load and create embeddings
        self.user_item_user_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_user_graph_emb_path), freeze=False).to(self.device)
        self.user_item_item_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_item_graph_emb_path), freeze=False).to(self.device)
        self.user_user_click_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_click_graph_emb_path), freeze=False).to(self.device)
        self.user_user_impression_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_imp_graph_emb_path), freeze=False).to(self.device)
        self.item_item_clicked_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_click_graph_emb_path), freeze=False).to(self.device)
        self.item_item_impressed_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_imp_graph_emb_path), freeze=False).to(self.device)

        # Load text embeddings
        user_text_emb_path = os.path.join(self.data_dir, 'text_embeddings/user_text_embeddings.pt')
        item_text_emb_path = os.path.join(self.data_dir, 'text_embeddings/item_text_embeddings.pt')
        self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
        self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)

    def load_graphs(self):
        self.user_item_graph = torch.load(os.path.join(self.data_dir, 'graph/user_item_graph.pt'), weights_only=False).to(self.device)
        self.user_graph = torch.load(os.path.join(self.data_dir, 'graph/user_graph.pt'), weights_only=False).to(self.device)
        self.item_graph = torch.load(os.path.join(self.data_dir, 'graph/item_graph.pt'), weights_only=False).to(self.device)

    def map_ids_to_indices(self, user_ids, item_ids):
        user_ids_list = user_ids.cpu().tolist()
        item_ids_list = item_ids.cpu().tolist()
        
        user_indices = torch.tensor([self.user_id_to_idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
        item_indices = torch.tensor([self.item_id_to_idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
        
        return user_indices, item_indices
    
    def map_graph_ids_to_indices(self, user_ids, item_ids, graph_type):
        user_ids_list = user_ids.cpu().tolist()
        item_ids_list = item_ids.cpu().tolist()

        if graph_type == 'user-item-user':
            user_indices = torch.tensor([self.user_item_user_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'user-item-item':
            item_indices = torch.tensor([self.user_item_item_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        elif graph_type == 'user-user-click':
            user_indices = torch.tensor([self.user_user_click_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'user-user-impression':
            user_indices = torch.tensor([self.user_user_imp_id2idx.get(uid, 0) for uid in user_ids_list], device=self.device).long()
            return user_indices
        elif graph_type == 'item-item-click':
            item_indices = torch.tensor([self.item_item_click_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        elif graph_type == 'item-item-impression':
            item_indices = torch.tensor([self.item_item_imp_id2idx.get(iid, 0) for iid in item_ids_list], device=self.device).long()
            return item_indices
        else:
            raise ValueError("Invalid graph type")

    def _ultra_fast_extract_submatrix(self, matrix, row_ids, col_ids, matrix_type, max_samples=100):
        """超级快速的矩阵提取方法 - 针对巨大矩阵的紧急优化"""
        batch_size = len(row_ids)
        
        # 如果矩阵太大，直接返回一个极其稀疏的随机矩阵
        if isinstance(matrix, torch.sparse.Tensor) and matrix._nnz() > 10000000:
            # 只采样很少的边
            num_edges = min(max_samples, batch_size * 2)
            
            # 随机生成一些边
            rows = torch.randint(0, batch_size, (num_edges,), device=self.device)
            cols = torch.randint(0, batch_size, (num_edges,), device=self.device)
            values = torch.ones(num_edges, device=self.device)
            
            return torch.sparse_coo_tensor(
                torch.stack([rows, cols]),
                values,
                (batch_size, batch_size),
                device=self.device
            )
        
        # 对于较小的矩阵，使用简化的提取
        try:
            # 快速提取，不做复杂的映射
            if matrix_type == 'user-item':
                # 对于user-item矩阵，直接提取小块
                return torch.sparse_coo_tensor(
                    torch.empty((2, 0), dtype=torch.long),
                    torch.empty(0),
                    (len(row_ids), len(col_ids)),
                    device=self.device
                )
            else:
                # 对于user-user或item-item矩阵，创建对角矩阵
                indices = torch.arange(batch_size, device=self.device)
                values = torch.ones(batch_size, device=self.device)
                return torch.sparse_coo_tensor(
                    torch.stack([indices, indices]),
                    values,
                    (batch_size, batch_size),
                    device=self.device
                )
        except:
            # 如果出错，返回空矩阵
            return torch.sparse_coo_tensor(
                torch.empty((2, 0), dtype=torch.long),
                torch.empty(0),
                (len(row_ids), len(col_ids)),
                device=self.device
            )

    def build_batch(self, user_ids, item_ids, labels):
        """超快速批次构建 - 跳过耗时的矩阵操作"""
        user_ids = user_ids.to(self.device).long()
        item_ids = item_ids.to(self.device).long()
        
        # 快速获取索引
        user_indices, item_indices = self.map_ids_to_indices(user_ids, item_ids)
        
        # 获取嵌入
        user_feature_emb = self.user_feature_embeddings(user_indices)
        item_feature_emb = self.item_feature_embeddings(item_indices)
        
        # 获取图嵌入索引
        graph_indices = {}
        graph_indices['user-item-user'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-item-user')
        graph_indices['user-item-item'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-item-item')
        graph_indices['user-user-click'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-user-click')
        graph_indices['user-user-impression'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-user-impression')
        graph_indices['item-item-click'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'item-item-click')
        graph_indices['item-item-impression'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'item-item-impression')
        
        # 合并嵌入
        enriched_embeddings = {
            'user_item_user': torch.cat([user_feature_emb, self.user_item_user_graph_embeddings(graph_indices['user-item-user'])], dim=-1),
            'user_item_item': torch.cat([item_feature_emb, self.user_item_item_graph_embeddings(graph_indices['user-item-item'])], dim=-1),
            'user_co_click': torch.cat([user_feature_emb, self.user_user_click_graph_embeddings(graph_indices['user-user-click'])], dim=-1),
            'user_co_impression': torch.cat([user_feature_emb, self.user_user_impression_graph_embeddings(graph_indices['user-user-impression'])], dim=-1),
            'item_co_clicked': torch.cat([item_feature_emb, self.item_item_clicked_graph_embeddings(graph_indices['item-item-click'])], dim=-1),
            'item_co_impression': torch.cat([item_feature_emb, self.item_item_impressed_graph_embeddings(graph_indices['item-item-impression'])], dim=-1),
            'user_text_embeddings': self.user_text_embeddings[user_indices],
            'item_text_embeddings': self.item_text_embeddings[item_indices],
        }
        
        def extract_subgraph(edge_index, node_ids, total_nodes):
            node_ids_unique = node_ids.unique().to(edge_index.device)
            sub_edge_index, _ = subgraph(
                node_ids_unique, edge_index=edge_index, relabel_nodes=True, num_nodes=total_nodes
            )
            return sub_edge_index

        def edge_index_to_sparse(edge_index, size):
            # edge_index: [2, num_edges]
            values = torch.ones(edge_index.shape[1], dtype=torch.float32, device=edge_index.device)
            return torch.sparse_coo_tensor(edge_index, values, size=size).coalesce()

        # 假设 user_indices/item_indices 是 LongTensor，且已经在 device 上
        user_indices_cpu = user_indices.to('cpu')
        item_indices_cpu = item_indices.to('cpu')

        batch = {
            # Embedding 部分
            "user_item_user_embeddings": enriched_embeddings["user_item_user"],
            "user_item_item_embeddings": enriched_embeddings["user_item_item"],
            "user_co_click_embeddings": enriched_embeddings["user_co_click"],
            "user_co_impression_embeddings": enriched_embeddings["user_co_impression"],
            "item_co_clicked_embeddings": enriched_embeddings["item_co_clicked"],
            "item_co_impressed_embeddings": enriched_embeddings["item_co_impression"],
            "user_text_embeddings": enriched_embeddings['user_text_embeddings'],
            "item_text_embeddings": enriched_embeddings['item_text_embeddings'],

            # 稠密矩阵部分（交互）
            "click_matrix": self.click_matrix.to_dense()[user_indices_cpu][:, item_indices_cpu].to(self.device),
            "impression_matrix": self.impression_matrix.to_dense()[user_indices_cpu][:, item_indices_cpu].to(self.device),

            # 稀疏图结构（稀疏子图提取）
            "user_edge_type1": edge_index_to_sparse(extract_subgraph(self.user_edge_type1_index, user_indices, self.num_users),size=(len(user_indices), len(user_indices))),
            "user_edge_type2": edge_index_to_sparse(extract_subgraph(self.user_edge_type2_index, user_indices, self.num_users),size=(len(user_indices), len(user_indices))),
            "item_edge_type1": edge_index_to_sparse(extract_subgraph(self.item_edge_type1_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
            "item_edge_type2": edge_index_to_sparse(extract_subgraph(self.item_edge_type2_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
        }    
        return batch