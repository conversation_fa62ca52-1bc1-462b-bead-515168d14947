#!/bin/bash

# 设置数据目录和模型保存目录 - BookCrossing Dataset
DATA_DIR="/data/datasets/processed_datasets/bookcrossing"
MODEL_SAVE_DIR="/data/datasets/processed_datasets/bookcrossing/ablation_study_losses"
BATCH_SIZE=128
NUM_EPOCHS=1
LEARNING_RATE=5e-4
TEMPERATURE=0.05
WANDB_PROJECT="GraphLLM4CTR_Ablation_Study_BookCrossing"

# 创建保存目录
mkdir -p $MODEL_SAVE_DIR

# 切换到正确的目录
cd /root/code

# 运行消融实验1：移除 User-Item Loss (λ1)
echo "Running ablation: without user-item loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_user_item" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

# 运行消融实验2：移除 User Graph Loss (λ2, λ4)
echo "Running ablation: without user graph loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_user_graph" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

# 运行消融实验3：移除 Item Graph Loss (λ3, λ5)
echo "Running ablation: without item graph loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_item_graph" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

# 运行消融实验4：移除 User-Item Loss & Item Graph Loss (λ1, λ3, λ5)
echo "Running ablation: without user-item loss and item graph loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_user_item_and_item_graph" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

# 运行消融实验5：移除 User-Item Loss & User Graph Loss (λ1, λ2, λ4)
echo "Running ablation: without user-item loss and user graph loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_user_item_and_user_graph" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

# 运行消融实验6：移除 User Graph Loss & Item Graph Loss (λ2, λ3, λ4, λ5)
echo "Running ablation: without user graph loss and item graph loss..."
python -m GraphLLM4CTR.ablation_study_losses.ablation_train \
    --data_dir $DATA_DIR \
    --model_save_dir $MODEL_SAVE_DIR \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE \
    --temperature $TEMPERATURE \
    --ablation_type "wo_user_graph_and_item_graph" \
    --wandb_project $WANDB_PROJECT \
    --dataset_type "bookcrossing"

echo "All ablation experiments completed for BookCrossing dataset!"
