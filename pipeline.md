## Pipeline-EN

1. Graph Construction

   - User-Item Graph (Bipartite)
     - Nodes: Users and Items
     - Edges (2 types): 
       - Type 1 (click): User clicked on the item
       - Type 2 (impression): User was shown the item — regardless of whether they clicked or not.
   - User Graph (Multi-relational)
     - Nodes: Users
     - Edges(2 types):
       - Type 1:  Users who clicked the same item.
       - Type 2: Users who were shown the same item
   - Item Graph (Multi-relational)
     - Nodes: Items
     - Edges(2 types):
       - Type 1:Items that were clicked by the same user
       - Type 2: Items that were shown to the same user.

2. Embedding

   - Feature Embedding
     - Categorical and numerical features are one-hot encoded, then embedded (embedding lookup table).
     - such as gender feature ("female") -> index -> embedding lookup; age categorical feature ("0-18") -> index -> embedding lookup
     - Text features like title are not used here ->  saved for later in the CTR model.
   - Graph Embedding
     - Use Node2Vec to capture graph structure
     - For user-item graph, edge weights depend on behavior types
     - For user graph and item graph, run Node2Vec on each subgraph (one per edge type).
   - Fusion of Embeddings
     - For each node: $\text{Enriched Embedding} = [\text{Graph Embedding} \parallel \text{Feature Embedding}]\\$
   - Text Embedding for Alignment (Contrastive Learning Stage)
     - Construct text prompts from node features (e.g., gender, category) — not actual raw text like titles.
     - Used only for alignment with graph representations — not used in CTR prediction model
     - Example: "This user likes genre: romantic, age: 25…"; "This is a movie, genre is comedy…”

3. Alignment via Contrastive Learning

   - Q-Former Embedding
     - Graph-Q-Former: Processes enriched graph embeddings (Fusion of Embeddings) → modality-aware graph representations.
     - Text-Q-Former: Processes prompt-based text embeddings → modality-aware text representations.
   - Contrastive Objectives
     - Loss 1: User-Item Graph
       - Positive: user clicked item
       - Negatives:
         - Strong: impressed but not clicked
         - Weak: randomly sampled item
       - Contrastive loss between user node embedding and item node embedding.
     - Loss 2: User Graph
       - Subgraph 1: structure-based neighbor contrast based on subgraph (edge type1)
       - Subgraph 2: structure-based neighbor contrast based on subgraph (edge type2)
       - Final loss = combination of subgraph contrastive losses.
       - Contrastive loss between user node embedding and user node embedding.
     - Loss3: Item Graph
       - Same contrastive approach as user graph.
     - Loss4: User Graph vs Text
       - Align user graph embeddings with text-Q-former outputs (this node's corresponding text embedding)
     - Loss5: Item Graph vs Text
       - Align item graph embeddings with text-Q-former outputs (this node's corresponding text embedding)
   - Final Contrastive Loss: $\text{Total Loss} = \lambda_1 \cdot \text{loss1} + \lambda_2 \cdot \text{loss2} + \lambda_3 \cdot \text{loss3} + \lambda_4 \cdot \text{loss4} + \lambda_5 \cdot \text{loss5}$

4. CTR prediction model

   - Initial Embedding (for Downstream Prediction)

     - For each user/item node:
       - Graph Embedding: from contrastive learning output.
       - Text Embedding: derived from actual text features, such as titles.
       - Text Embedding Process: $\text{Title (sentence)} \rightarrow \text{Frozen Text Encoder} \rightarrow \text{Text-Q-Former} \rightarrow \text{Text Embedding}$
       - Final input representation:  $\text{Initial Node Representation} = [\text{Graph Embedding} \parallel \text{Text Feature Embedding}]$

   - Graph Encoding with LLM-Enhanced GNNs

     - Apply GNN encoders:
       - HGT on user-item graph
       - 2× R-GCNs on user graph and item graph
     - LLM-enhanced Message Update Function: replace the original message update function in HGT or R-GCN
       - LLM-enhanced message update function: $\text{Input} \rightarrow \text{Projector} \rightarrow \text{Text-Q-Former(frozen)} \rightarrow \text{Front Layers of Frozen LLM} \rightarrow \text{Adapter}$
       - Output used in GNN message passing
     - Add residual connections across GNN layers.

   - Expert-Based Fusion

     - Define multiple MLP-based experts:

       - User Expert, Item Expert, and Shared Experts
       - Also define gating networks to assign expert scores for each user/item.

     - Fusion Logic:

       - For users:
         - Fuse HGT-based user embedding + R-GCN-based user embeddings.

       - For items:
         - Fuse HGT-based item embedding + R-GCN-based item embeddings.
       - Use learned expert scores to weight and combine expert outputs.

   - Final CTR Prediction

     - Concatenate final user and item embeddings: $[\text{User Embedding} \parallel \text{Item Embedding}]$
     - Feed into: MLP and sigmoid activation
     - BCE loss between predicted click probability and ground truth.
