import pandas as pd
import gzip
import json
from tqdm import tqdm

def load_reviews_json(file_path):
    """
    Load the Sports_and_Outdoors_5.json file.
    Each line is a separate JSON object containing review data.
    """
    reviews = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading reviews"):
            try:
                review = json.loads(line.strip())
                reviews.append(review)
            except json.JSONDecodeError as e:
                print(f"Error parsing line: {e}")
                continue
    
    return reviews

def load_metadata_json(file_path):
    """
    Load the meta_Sports_and_Outdoors.json file (gzipped).
    Each line is a separate JSON object containing product metadata.
    """
    metadata = []
    
    with gzip.open(file_path, 'rt', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading metadata"):
            try:
                meta = json.loads(line.strip())
                metadata.append(meta)
            except json.JSONDecodeError as e:
                print(f"Error parsing line: {e}")
                continue
    
    return metadata

# 5-core
reviews = load_reviews_json('/data/datasets/amazon/Sports_and_Outdoors_5.json')
reviews_df = pd.DataFrame(reviews)
print(reviews_df.head())
print(reviews_df.columns)

# # rating csv of amazon
# rating = pd.read_csv('/data/datasets/amazon/Sports_and_Outdoors.csv', names=['asin','revierID','rating','timestamp'], nrows=1000)
# print(rating.head())
# print(rating.columns)

# # meta info
# metadata = load_metadata_json('meta_Sports_and_Outdoors.json')
