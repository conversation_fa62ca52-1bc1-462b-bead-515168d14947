import torch
import numpy as np
import matplotlib.pyplot as plt
import torch.nn.functional as F
import argparse
import os
from sklearn.manifold import TSNE
from sklearn.metrics import roc_curve, auc

def evaluate_contrastive_learning(args):
    """Comprehensive evaluation of contrastive learning performance"""

    # Create save directory
    os.makedirs(args.save_dir, exist_ok=True)

    # Determine device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # ===== ITEM EMBEDDINGS EVALUATION =====
    print("\n===== EVALUATING ITEM EMBEDDINGS =====")

    # Load item text embeddings
    print("Loading item embeddings...")
    item_text = torch.load(args.item_text_path)
    # Move to CPU if on CUDA to avoid memory issues
    item_text = item_text.cpu()

    # Load and combine item-item graph embeddings (click and impression)
    item_click = torch.load(args.item_click_path).cpu()
    item_imp = torch.load(args.item_imp_path).cpu()

    # Average the click and impression embeddings
    print("Combining item-item graph embeddings...")
    item_graph = (item_click + item_imp) / 2
    print(f"Item graph shape: {item_graph.shape}, Item text shape: {item_text.shape}")

    # Load before embeddings if available
    if args.before_item_text_path and args.before_item_click_path and args.before_item_imp_path:
        print("Loading pre-contrastive item embeddings...")
        item_text_before = torch.load(args.before_item_text_path).cpu()
        item_click_before = torch.load(args.before_item_click_path).cpu()
        item_imp_before = torch.load(args.before_item_imp_path).cpu()

        # Average the before embeddings
        item_graph_before = (item_click_before + item_imp_before) / 2

        # Ensure dimensions match
        if item_text_before.shape[-1] != item_graph_before.shape[-1]:
            print("Reducing text dimension to match graph dimension...")
            reduce_text = torch.nn.Linear(item_text_before.shape[-1], item_graph_before.shape[-1]).cpu()
            with torch.no_grad():  # Disable gradient tracking
                item_text_before = reduce_text(item_text_before).detach()

    # Create item-specific save directory
    item_save_dir = os.path.join(args.save_dir, "item_evaluation")
    os.makedirs(item_save_dir, exist_ok=True)

    # 1. Visualize item similarity distributions
    print("Visualizing item similarity distributions...")
    plot_similarity_distributions(item_graph, item_text, "Item After Contrastive Learning", item_save_dir)
    if args.before_item_text_path and args.before_item_click_path and args.before_item_imp_path:
        plot_similarity_distributions(item_graph_before, item_text_before, "Item Before Contrastive Learning", item_save_dir)

    # 2. Calculate item alignment and uniformity
    print("Calculating item alignment and uniformity metrics...")
    alignment, uniformity = calculate_alignment_uniformity(item_graph, item_text)
    print(f"Item Alignment: {alignment:.4f} (lower is better)")
    print(f"Item Uniformity: {uniformity:.4f} (lower is better)")

    if args.before_item_text_path and args.before_item_click_path and args.before_item_imp_path:
        alignment_before, uniformity_before = calculate_alignment_uniformity(item_graph_before, item_text_before)
        print(f"Item Alignment before: {alignment_before:.4f}")
        print(f"Item Uniformity before: {uniformity_before:.4f}")
        print(f"Item Improvement in alignment: {alignment_before - alignment:.4f}")
        print(f"Item Improvement in uniformity: {uniformity_before - uniformity:.4f}")

    # 3. Visualize item embedding space
    print("Visualizing item embedding space...")
    visualize_embedding_space(item_graph, item_text, item_save_dir, title_prefix="Item")

    # 4. Evaluate item retrieval performance
    print("Evaluating item retrieval performance...")
    evaluate_cross_modal_retrieval(item_graph, item_text, item_save_dir, prefix="Item")

    # ===== USER EMBEDDINGS EVALUATION =====
    if args.user_text_path and args.user_click_path and args.user_imp_path:
        print("\n===== EVALUATING USER EMBEDDINGS =====")

        # Load user text embeddings
        print("Loading user embeddings...")
        user_text = torch.load(args.user_text_path).cpu()

        # Load and combine user-user graph embeddings (click and impression)
        user_click = torch.load(args.user_click_path).cpu()
        user_imp = torch.load(args.user_imp_path).cpu()

        # Average the click and impression embeddings
        print("Combining user-user graph embeddings...")
        user_graph = (user_click + user_imp) / 2
        print(f"User graph shape: {user_graph.shape}, User text shape: {user_text.shape}")

        # Load before embeddings if available
        if args.before_user_text_path and args.before_user_click_path and args.before_user_imp_path:
            print("Loading pre-contrastive user embeddings...")
            user_text_before = torch.load(args.before_user_text_path).cpu()
            user_click_before = torch.load(args.before_user_click_path).cpu()
            user_imp_before = torch.load(args.before_user_imp_path).cpu()

            # Average the before embeddings
            user_graph_before = (user_click_before + user_imp_before) / 2

            # Ensure dimensions match
            if user_text_before.shape[-1] != user_graph_before.shape[-1]:
                print("Reducing text dimension to match graph dimension...")
                reduce_text = torch.nn.Linear(user_text_before.shape[-1], user_graph_before.shape[-1]).cpu()
                with torch.no_grad():  # Disable gradient tracking
                    user_text_before = reduce_text(user_text_before).detach()

        # Create user-specific save directory
        user_save_dir = os.path.join(args.save_dir, "user_evaluation")
        os.makedirs(user_save_dir, exist_ok=True)

        # 1. Visualize user similarity distributions
        print("Visualizing user similarity distributions...")
        plot_similarity_distributions(user_graph, user_text, "User After Contrastive Learning", user_save_dir)
        if args.before_user_text_path and args.before_user_click_path and args.before_user_imp_path:
            plot_similarity_distributions(user_graph_before, user_text_before, "User Before Contrastive Learning", user_save_dir)

        # 2. Calculate user alignment and uniformity
        print("Calculating user alignment and uniformity metrics...")
        alignment, uniformity = calculate_alignment_uniformity(user_graph, user_text)
        print(f"User Alignment: {alignment:.4f} (lower is better)")
        print(f"User Uniformity: {uniformity:.4f} (lower is better)")

        if args.before_user_text_path and args.before_user_click_path and args.before_user_imp_path:
            alignment_before, uniformity_before = calculate_alignment_uniformity(user_graph_before, user_text_before)
            print(f"User Alignment before: {alignment_before:.4f}")
            print(f"User Uniformity before: {uniformity_before:.4f}")
            print(f"User Improvement in alignment: {alignment_before - alignment:.4f}")
            print(f"User Improvement in uniformity: {uniformity_before - uniformity:.4f}")

        # 3. Visualize user embedding space
        print("Visualizing user embedding space...")
        visualize_embedding_space(user_graph, user_text, user_save_dir, title_prefix="User")

        # 4. Evaluate user retrieval performance
        print("Evaluating user retrieval performance...")
        evaluate_cross_modal_retrieval(user_graph, user_text, user_save_dir, prefix="User")

    print("\nEvaluation complete!")

def plot_similarity_distributions(embeddings1, embeddings2, title, save_dir):
    """Plot distribution of similarities between embeddings"""
    # Ensure embeddings are 2D
    if embeddings1.dim() > 2:
        embeddings1 = embeddings1.mean(dim=1)
    if embeddings2.dim() > 2:
        embeddings2 = embeddings2.mean(dim=1)

    # Ensure both are on the same device (CPU), detached from computation graph, and in float32 format
    embeddings1 = embeddings1.detach().cpu().float()
    embeddings2 = embeddings2.detach().cpu().float()

    # Normalize embeddings
    embeddings1 = F.normalize(embeddings1, dim=1)
    embeddings2 = F.normalize(embeddings2, dim=1)

    # Calculate similarity matrix
    similarity = torch.matmul(embeddings1, embeddings2.t())

    # Get positive pair similarities (diagonal)
    pos_sim = torch.diagonal(similarity).numpy()

    # Get negative pair similarities (off-diagonal)
    mask = ~torch.eye(similarity.shape[0], dtype=bool, device=similarity.device)
    neg_sim = similarity[mask].numpy()

    # Plot distributions
    plt.figure(figsize=(10, 6))
    plt.hist(pos_sim, bins=50, alpha=0.5, label='Positive Pairs', density=True)
    plt.hist(neg_sim, bins=50, alpha=0.5, label='Negative Pairs', density=True)
    plt.axvline(x=np.mean(pos_sim), color='blue', linestyle='--', label=f'Pos Mean: {np.mean(pos_sim):.3f}')
    plt.axvline(x=np.mean(neg_sim), color='orange', linestyle='--', label=f'Neg Mean: {np.mean(neg_sim):.3f}')
    plt.title(f"Similarity Distribution - {title}")
    plt.xlabel('Cosine Similarity')
    plt.ylabel('Density')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Calculate separation metric
    separation = np.mean(pos_sim) - np.mean(neg_sim)
    plt.annotate(f'Separation: {separation:.3f}', xy=(0.05, 0.95), xycoords='axes fraction')

    # Save figure
    os.makedirs(save_dir, exist_ok=True)
    plt.savefig(os.path.join(save_dir, f"similarity_distribution_{title.lower().replace(' ', '_')}.png"))
    plt.close()

    # Calculate and plot ROC curve
    plt.figure(figsize=(8, 6))

    # Create labels (1 for positive pairs, 0 for negative pairs)
    labels = np.zeros(len(pos_sim) + len(neg_sim))
    labels[:len(pos_sim)] = 1

    # Combine similarities
    scores = np.concatenate([pos_sim, neg_sim])

    # Calculate ROC curve
    fpr, tpr, _ = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    # Plot ROC curve
    plt.plot(fpr, tpr, lw=2, label=f'ROC curve (AUC = {roc_auc:.3f})')
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title(f'ROC Curve - {title}')
    plt.legend(loc="lower right")
    plt.grid(True, alpha=0.3)

    # Save figure
    plt.savefig(os.path.join(save_dir, f"roc_curve_{title.lower().replace(' ', '_')}.png"))
    plt.close()

    return separation, roc_auc

def calculate_alignment_uniformity(embeddings1, embeddings2):
    """Calculate alignment and uniformity metrics"""
    # Ensure embeddings are 2D
    if embeddings1.dim() > 2:
        embeddings1 = embeddings1.mean(dim=1)
    if embeddings2.dim() > 2:
        embeddings2 = embeddings2.mean(dim=1)

    # Ensure both are on the same device (CPU), detached from computation graph, and in float32 format
    embeddings1 = embeddings1.detach().cpu().float()
    embeddings2 = embeddings2.detach().cpu().float()

    # Normalize embeddings
    embeddings1 = F.normalize(embeddings1, dim=1)
    embeddings2 = F.normalize(embeddings2, dim=1)

    # Alignment: mean squared distance between positive pairs
    alignment = ((embeddings1 - embeddings2) ** 2).sum(dim=1).mean().item()

    # Uniformity: log of average pairwise Gaussian potential
    t = 2  # Temperature parameter
    uniformity1 = torch.pdist(embeddings1, p=2).pow(2).mul(-t).exp().mean().log().item()
    uniformity2 = torch.pdist(embeddings2, p=2).pow(2).mul(-t).exp().mean().log().item()
    uniformity = (uniformity1 + uniformity2) / 2

    return alignment, uniformity

def visualize_embedding_space(embeddings1, embeddings2, save_dir, n_samples=2000, title_prefix=""):
    """Visualize embedding space using t-SNE"""
    # Ensure embeddings are 2D
    if embeddings1.dim() > 2:
        embeddings1 = embeddings1.mean(dim=1)
    if embeddings2.dim() > 2:
        embeddings2 = embeddings2.mean(dim=1)

    # Ensure both are on the same device (CPU), detached from computation graph, and in float32 format
    embeddings1 = embeddings1.detach().cpu().float()
    embeddings2 = embeddings2.detach().cpu().float()

    # Normalize embeddings
    embeddings1 = F.normalize(embeddings1, dim=1)
    embeddings2 = F.normalize(embeddings2, dim=1)

    # Sample if too many points
    if len(embeddings1) > n_samples:
        indices = torch.randperm(len(embeddings1))[:n_samples]
        embeddings1 = embeddings1[indices]
        embeddings2 = embeddings2[indices]

    # Combine embeddings
    combined = torch.cat([embeddings1, embeddings2], dim=0).numpy()

    # Create labels (0 for embeddings1, 1 for embeddings2)
    labels = np.zeros(len(combined))
    labels[len(embeddings1):] = 1

    # Apply t-SNE
    print("Running t-SNE...")
    tsne = TSNE(n_components=2, perplexity=10, n_iter=1000, random_state=42)
    reduced = tsne.fit_transform(combined)

    # Plot
    plt.figure(figsize=(10, 8))
    plt.scatter(reduced[:len(embeddings1), 0], reduced[:len(embeddings1), 1],
                c='blue', label=f'{title_prefix} Graph Embeddings', alpha=0.5, s=10)
    plt.scatter(reduced[len(embeddings1):, 0], reduced[len(embeddings1):, 1],
                c='red', label=f'{title_prefix} Text Embeddings', alpha=0.5, s=10)

    title = "t-SNE Visualization of Embedding Space"
    if title_prefix:
        title = f"{title_prefix} {title}"
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Save figure
    os.makedirs(save_dir, exist_ok=True)
    filename = "embedding_space_tsne.png"
    if title_prefix:
        filename = f"{title_prefix.lower()}_embedding_space_tsne.png"
    plt.savefig(os.path.join(save_dir, filename))
    plt.close()

def evaluate_cross_modal_retrieval(embeddings1, embeddings2, save_dir, k_values=[1, 5, 10, 20, 50], prefix=""):
    """Evaluate cross-modal retrieval performance"""
    # Ensure embeddings are 2D
    if embeddings1.dim() > 2:
        embeddings1 = embeddings1.mean(dim=1)
    if embeddings2.dim() > 2:
        embeddings2 = embeddings2.mean(dim=1)

    # Ensure both are on the same device (CPU), detached from computation graph, and in float32 format
    embeddings1 = embeddings1.detach().cpu().float()
    embeddings2 = embeddings2.detach().cpu().float()

    # Normalize embeddings
    embeddings1 = F.normalize(embeddings1, dim=1)
    embeddings2 = F.normalize(embeddings2, dim=1)

    # Compute similarity matrices
    sim_1_to_2 = torch.matmul(embeddings1, embeddings2.t())
    sim_2_to_1 = sim_1_to_2.t()

    # Evaluate retrieval in both directions
    results = {}

    # Graph -> Text retrieval
    direction1 = "Graph->Text"
    if prefix:
        direction1 = f"{prefix} {direction1}"
    results[direction1] = {}

    for k in k_values:
        _, indices = sim_1_to_2.topk(k, dim=1)
        # Diagonal elements are the ground truth
        correct = torch.sum(indices == torch.arange(len(embeddings1), device=indices.device).unsqueeze(1)).item()
        recall_at_k = correct / len(embeddings1)
        results[direction1][f"R@{k}"] = recall_at_k

    # Text -> Graph retrieval
    direction2 = "Text->Graph"
    if prefix:
        direction2 = f"{prefix} {direction2}"
    results[direction2] = {}

    for k in k_values:
        _, indices = sim_2_to_1.topk(k, dim=1)
        # Diagonal elements are the ground truth
        correct = torch.sum(indices == torch.arange(len(embeddings2), device=indices.device).unsqueeze(1)).item()
        recall_at_k = correct / len(embeddings2)
        results[direction2][f"R@{k}"] = recall_at_k

    # Print results
    title = "Cross-Modal Retrieval Results"
    if prefix:
        title = f"{prefix} {title}"

    print(f"\n{title}:")
    print("-----------------------------")
    for direction, metrics in results.items():
        print(f"\n{direction}:")
        for k, value in metrics.items():
            print(f"  {k}: {value:.4f}")

    # Save results to file
    filename = "retrieval_results.txt"
    if prefix:
        filename = f"{prefix.lower()}_retrieval_results.txt"

    with open(os.path.join(save_dir, filename), "w") as f:
        f.write(f"{title}:\n")
        f.write("-----------------------------\n")
        for direction, metrics in results.items():
            f.write(f"\n{direction}:\n")
            for k, value in metrics.items():
                f.write(f"  {k}: {value:.4f}\n")

    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate contrastive learning performance")

    # Item embedding paths (required)
    parser.add_argument("--item_text_path", type=str, required=True,
                        help="Path to item text embeddings")
    parser.add_argument("--item_click_path", type=str, required=True,
                        help="Path to item-item click graph embeddings")
    parser.add_argument("--item_imp_path", type=str, required=True,
                        help="Path to item-item impression graph embeddings")

    # User embedding paths (optional)
    parser.add_argument("--user_text_path", type=str,
                        help="Path to user text embeddings")
    parser.add_argument("--user_click_path", type=str,
                        help="Path to user-user click graph embeddings")
    parser.add_argument("--user_imp_path", type=str,
                        help="Path to user-user impression graph embeddings")

    # Before contrastive learning - item embeddings (optional)
    parser.add_argument("--before_item_text_path", type=str,
                        help="Path to item text embeddings before contrastive learning")
    parser.add_argument("--before_item_click_path", type=str,
                        help="Path to item-item click graph embeddings before contrastive learning")
    parser.add_argument("--before_item_imp_path", type=str,
                        help="Path to item-item impression graph embeddings before contrastive learning")

    # Before contrastive learning - user embeddings (optional)
    parser.add_argument("--before_user_text_path", type=str,
                        help="Path to user text embeddings before contrastive learning")
    parser.add_argument("--before_user_click_path", type=str,
                        help="Path to user-user click graph embeddings before contrastive learning")
    parser.add_argument("--before_user_imp_path", type=str,
                        help="Path to user-user impression graph embeddings before contrastive learning")

    # Output directory
    parser.add_argument("--save_dir", type=str, default="./visualization_results",
                        help="Directory to save results")

    args = parser.parse_args()
    evaluate_contrastive_learning(args)