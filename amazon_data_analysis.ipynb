{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Amazon Dataset Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23261/98958167.py:14: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html\n", "  import pkg_resources\n"]}], "source": ["import pandas as pd\n", "import gzip\n", "import json\n", "from tqdm import tqdm\n", "from datetime import datetime\n", "import os\n", "import re\n", "import unicodedata\n", "import html\n", "import string\n", "from functools import wraps\n", "from urllib.parse import unquote\n", "\n", "import pkg_resources\n", "import plane\n", "from plane.pattern import Regex\n", "\n", "import ast"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["tqdm.pandas()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_reviews_json(file_path):\n", "    \"\"\"\n", "    Load the Sports_and_Outdoors_5.json file.\n", "    Each line is a separate JSON object containing review data.\n", "    \"\"\"\n", "    reviews = []\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in tqdm(f, desc=\"Loading reviews\"):\n", "            try:\n", "                review = json.loads(line.strip())\n", "                reviews.append(review)\n", "            except json.JSONDecodeError as e:\n", "                print(f\"Error parsing line: {e}\")\n", "                continue\n", "    \n", "    return reviews"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_metadata_json(file_path):\n", "    \"\"\"\n", "    Load the meta_Sports_and_Outdoors.json file (gzipped).\n", "    Each line is a separate JSON object containing product metadata.\n", "    \"\"\"\n", "    metadata = []\n", "    \n", "    with gzip.open(file_path, 'rt', encoding='utf-8') as f:\n", "        for line in tqdm(f, desc=\"Loading metadata\"):\n", "            try:\n", "                meta = json.loads(line.strip())\n", "                metadata.append(meta)\n", "            except json.JSONDecodeError as e:\n", "                print(f\"Error parsing line: {e}\")\n", "                continue\n", "    \n", "    return metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Dataset"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading reviews: 2839940it [00:23, 121669.28it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 2839940 reviews\n", "DataFrame shape: (2839940, 8)\n", "Columns: ['overall', 'verified', 'reviewTime', 'reviewerID', 'asin', 'reviewerName', 'unixReviewTime', 'unixReviewTime_convert']\n", "Remove Duplicates DataFrame shape: (2685991, 8)\n", "Keep Useful Columns: ['overall', 'reviewerID', 'asin', 'unixReviewTime_convert']\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>overall</th>\n", "      <th>reviewerID</th>\n", "      <th>asin</th>\n", "      <th>unixReviewTime_convert</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.0</td>\n", "      <td>A180LQZBUWVOLF</td>\n", "      <td>0000032034</td>\n", "      <td>2015-06-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1.0</td>\n", "      <td>ATMFGKU5SVEYY</td>\n", "      <td>0000032034</td>\n", "      <td>2015-04-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5.0</td>\n", "      <td>A1QE70QBJ8U6ZG</td>\n", "      <td>0000032034</td>\n", "      <td>2015-01-13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5.0</td>\n", "      <td>A22CP6Z73MZTYU</td>\n", "      <td>0000032034</td>\n", "      <td>2014-12-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4.0</td>\n", "      <td>A22L28G8NRNLLN</td>\n", "      <td>0000032034</td>\n", "      <td>2014-12-15</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   overall      reviewerID        asin unixReviewTime_convert\n", "0      5.0  A180LQZBUWVOLF  0000032034             2015-06-03\n", "1      1.0   ATMFGKU5SVEYY  0000032034             2015-04-01\n", "2      5.0  A1QE70QBJ8U6ZG  0000032034             2015-01-13\n", "3      5.0  A22CP6Z73MZTYU  0000032034             2014-12-23\n", "4      4.0  A22L28G8NRNLLN  0000032034             2014-12-15"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load 5-core\n", "reviews = load_reviews_json('/data/datasets/amazon/Sports_and_Outdoors_5.json')\n", "print(f\"Loaded {len(reviews)} reviews\")\n", "\n", "# Convert to DataFrame\n", "reviews_df = pd.DataFrame(reviews)\n", "# Remove useless columns\n", "reviews_df = reviews_df.drop(columns=['style','vote','image','summary','reviewText'])\n", "# Process time\n", "reviews_df['unixReviewTime_convert'] = reviews_df['unixReviewTime'].apply(lambda x: datetime.utcfromtimestamp(x))\n", "print(f\"DataFrame shape: {reviews_df.shape}\")\n", "print(f\"Columns: {list(reviews_df.columns)}\")\n", "reviews_df.head()\n", "\n", "reviews_df = reviews_df.drop_duplicates()\n", "print(f\"Remove Duplicates DataFrame shape: {reviews_df.shape}\")\n", "\n", "# Keep useful columns\n", "reviews_df = reviews_df[['overall','reviewerID','asin','unixReviewTime_convert']]\n", "print(f\"Keep Useful Columns: {list(reviews_df.columns)}\")\n", "reviews_df.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["overall\n", "5.0    1817238\n", "4.0     468488\n", "3.0     198846\n", "1.0     105091\n", "2.0      96328\n", "Name: count, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["reviews_df['overall'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading metadata: 962300it [00:55, 17216.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 962300 metadata entries\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Load metadata\n", "metadata = load_metadata_json('/data/datasets/amazon/meta_Sports_and_Outdoors.json')\n", "print(f\"Loaded {len(metadata)} metadata entries\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata DataFrame shape: (962300, 19)\n", "Metadata columns: ['category', 'tech1', 'description', 'fit', 'title', 'also_buy', 'tech2', 'brand', 'feature', 'rank', 'also_view', 'main_cat', 'similar_item', 'date', 'price', 'asin', 'imageURL', 'imageURLHighRes', 'details']\n"]}], "source": ["# Convert metadata to DataFrame\n", "metadata_df = pd.DataFrame(metadata)\n", "print(f\"Metadata DataFrame shape: {metadata_df.shape}\")\n", "print(f\"Metadata columns: {list(metadata_df.columns)}\")\n", "\n", "# Keep useful columns\n", "metadata_df = metadata_df[['asin','title','price','category','brand']]\n", "metadata_df['category'] = metadata_df['category'].astype(str)\n", "metadata_df = metadata_df.drop_duplicates()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2685991, 4)\n", "(957217, 5)\n"]}], "source": ["print(reviews_df.shape)\n", "print(metadata_df.shape)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2685991, 8)\n"]}], "source": ["# Merge data\n", "reviews_df['asin'] = reviews_df['asin'].astype(str)\n", "metadata_df['asin'] = metadata_df['asin'].astype(str)\n", "df_merge = pd.merge(reviews_df, metadata_df, on='asin', how='left')\n", "print(df_merge.shape)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["remove title nan (2682031, 8)\n", "remove price nan (2682031, 8)\n", "remove brand nan (2682031, 8)\n", "remove category nan (2682031, 8)\n"]}], "source": ["# Remove dirty data\n", "df_merge_clean = df_merge[~df_merge['title'].isna()]\n", "print('remove title nan', df_merge_clean.shape)\n", "\n", "df_merge_clean = df_merge_clean[~df_merge_clean['price'].isna()]\n", "print('remove price nan', df_merge_clean.shape)\n", "\n", "df_merge_clean = df_merge_clean[~df_merge_clean['brand'].isna()]\n", "print('remove brand nan', df_merge_clean.shape)\n", "\n", "df_merge_clean = df_merge_clean[~df_merge_clean['category'].isna()]\n", "print('remove category nan', df_merge_clean.shape)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def verbose(func):\n", "    @wraps(func)\n", "    def wrapper(self, *args, **kwargs):\n", "        self = func(self, *args, **kwargs)\n", "        # if self.verbose:\n", "        #     print('[DEBUG] After {:20} => \"{}\"'.format(\n", "        #         func.__name__, self._text))\n", "        return self\n", "    return wrapper\n", "\n", "def replace_func(_text):\n", "    _text = _text.group(1) + \" \" + _text.group(3)\n", "    return _text\n", "\n", "class SouthAmericaLan:\n", "    \"\"\"text preprocessing.\n", "\n", "    :param bool verbose: activate DEBUG model\n", "    :param dict norm_map: use customized punctuation normalization mappings\n", "    :param str segmenter: use custumized segmenter\n", "    \"\"\"\n", "    def __init__(self, verbose=False, norm_map=None, segmenter=None):\n", "        self._text = ''\n", "        self.verbose = verbose\n", "        self.punc = plane.Punctuation(norm_map) if norm_map else plane.punc\n", "        self.plane = plane.Plane()\n", "        self.segmenter = segmenter\n", "        self.british2american = None\n", "        self.tag_map = {\n", "            'html': plane.HTML,\n", "            'email': plane.EMAIL,\n", "            'space': plane.SPACE,\n", "            'url': plane.URL,\n", "        }\n", "        self.lang = {\n", "            'TW': plane.CHINESE,\n", "            'VN': plane.VIETNAMESE,\n", "            'TH': plane.THAI,\n", "            'EN': plane.ENGLISH,\n", "            'PT': plane.BraSCII,\n", "            'ES': plane.BraSCII,\n", "            'NUM': plane.NUMBER,\n", "        }\n", "        self.pattern1 = re.compile(r\"\"\"([a-z]+)(-)([a-z])\"\"\", re.VERBOSE)\n", "        self.pattern2 = re.compile(r\"\"\"([a-z]+)(\\.)([a-z])\"\"\", re.VERBOSE)\n", "\n", "    def update(self, text):\n", "        self._text = text\n", "        return self\n", "\n", "    @property\n", "    def text(self):\n", "        return self._text\n", "\n", "\n", "    @text.setter\n", "    def text(self, text):\n", "        self._text = text\n", "\n", "    def init_english_map(self, file=None):\n", "        if not file:\n", "            file = os.path.join(\n", "                pkg_resources.resource_filename('data_clean_module_dev','data/assets'),\n", "                'british_american.json'\n", "            )\n", "\n", "        with open(file) as f:\n", "            self.british2american = json.load(f)\n", "\n", "    @verbose\n", "    def american(self):\n", "        \"\"\"Transfer British English to American English\n", "        \"\"\"\n", "        if not self.british2american:\n", "            self.init_english_map()\n", "        self._text = ' '.join([self.british2american.get(word.lower(), word)\n", "                               for word in self._text.split(' ')])\n", "        return self\n", "\n", "\n", "    @verbose\n", "    def normalize_unicode(self, form='NFC'):\n", "        \"\"\"Unicode Normalization.\n", "\n", "        :param str form: Unicode format, 'NFC', 'NFKC', 'NFD', 'NFKD'\n", "\n", "        For more information:\n", "\n", "        - http://unicode.org/reports/tr15/\n", "        - https://docs.python.org/3.7/library/unicodedata.html\n", "        - https://unicode.org/charts/normalization/\n", "        \"\"\"\n", "        self._text = unicodedata.normalize(form, self._text)\n", "        return self\n", "\n", "    @verbose\n", "    def normalize_punctuation(self):\n", "        \"\"\"Transfer punctuations from other languages to English punctuations.\n", "        \"\"\"\n", "        self._text = self.punc.normalize(self._text)\n", "        return self\n", "    \n", "    @verbose\n", "    def remove_special_punctuation(self):\n", "        self._text = self.pattern1.sub(replace_func, self._text)\n", "        self._text = self.pattern2.sub(replace_func, self._text)\n", "        return self\n", "\n", "    @verbose\n", "    def remove_punctuation(self, white_list=[\"-\", \".\", \"+\", \"&\"]):\n", "        \"\"\"Remove all the punctuations belongs to Unicode::Category::[P]\n", "\n", "        - https://www.compart.com/en/unicode/category/Po\n", "        \"\"\"\n", "        \n", "        remove_punc = [item for item in string.punctuation if item not in white_list]\n", "        self._text = \"\".join([item if item not in remove_punc else \" \" for item in self._text])\n", "        self._text = \" \".join(self._text.split())\n", "        return self\n", "\n", "    @verbose\n", "    def unescape_html(self):\n", "        \"\"\"HTML Escape\n", "\n", "        - https://dev.w3.org/html5/html-author/charref\n", "        - https://www.freeformatter.com/html-entities.html\n", "        \"\"\"\n", "        self._text = html.unescape(self._text)\n", "        return self\n", "\n", "\n", "    @verbose\n", "    def unquote_url(self):\n", "        \"\"\"URL unquote\n", "        \"\"\"\n", "        self._text = unquote(self._text)\n", "        return self\n", "\n", "\n", "    @verbose\n", "    def filter_lang(self, lang=['TW', 'EN', 'VN', 'NUM', 'TH', 'PT','ES']):\n", "        \"\"\"Extract specific language characters from text.\n", "\n", "        :param list[str] lang: ['TW', 'EN', 'VN', 'TH', 'NUM']\n", "        \"\"\"\n", "        for lan in lang:\n", "            if lan not in self.lang:\n", "                raise NameError('Unknown lang: {}. Only support {}.'.format(\n", "                    lan, self.lang.keys()))\n", "\n", "        regex = sum([self.lang.get(lan) for lan in lang] + [plane.SPACE])\n", "        self._text = ''.join([t.value for t in\n", "                              plane.extract(self._text, regex)])\n", "        return self\n", "\n", "    @verbose\n", "    def remove_tag(self, tag=['html', 'url', 'email', 'space']):\n", "        \"\"\"The order of tags matters.\n", "        :param list[str] tag: ['html', 'url', 'email', 'space']\n", "        \"\"\"\n", "        if not tag:\n", "            return self\n", "        for t in tag:\n", "            if t not in self.tag_map:\n", "                raise NameError('Unknown tag: {}. Only support {}.'.format(\n", "                    t, self.tag_map.keys()))\n", "\n", "            self._text = plane.replace(self._text, self.tag_map[t])\n", "        return self\n", "\n", "    @verbose\n", "    def segment(self, region='PT'):\n", "        # self._text = plane.segment(self._text)\n", "        self._text = self._text.split()\n", "        return self\n", "\n", "    def lower_case(self, region='PT'):\n", "        return self._text.lower()\n", "\n", "\n", "    def preprocess(self, text, lan='PT'):\n", "        \"\"\"All-in-one method. It's suitable for the common circumstance\n", "\n", "        :param str text: text\n", "        :param str country: ['TW', 'TH', 'SG', 'MY', 'ID', 'VN', 'PH']\n", "        \"\"\"\n", "        return (self.update(text)\n", "                .normalize_unicode()\n", "                .unescape_html()\n", "                .american()\n", "                .filter_lang([lan, 'EN', 'NUM']\n", "                             if lan in ['TW', 'VN', 'TH', 'PT','ES']\n", "                             else ['EN', 'NUM'])\n", "                .remove_tag(['html', 'url', 'email', 'space'])\n", "                .remove_special_punctuation()\n", "                .remove_punctuation()\n", "                .lower_case()\n", "                )\n", "\n", "class AmazonTitleCleaner:\n", "    def __init__(self, verbose=False):\n", "        self.verbose = verbose\n", "        self.preprocessor = SouthAmericaLan(verbose=verbose)\n", "        \n", "    def clean_title(self, title):\n", "        \"\"\"\n", "        Clean Amazon product titles by:\n", "        1. Removing HTML entities\n", "        2. Normalizing unicode\n", "        3. Removing excessive punctuation\n", "        4. Standardizing spacing\n", "        5. Converting to lowercase\n", "        \"\"\"\n", "        if not title or pd.isna(title):\n", "            return \"\"\n", "            \n", "        # Basic cleaning\n", "        title = str(title).strip()\n", "        \n", "        # Process with our text preprocessor\n", "        clean_title = (self.preprocessor.update(title)\n", "                      .normalize_unicode()\n", "                      .unescape_html()\n", "                      .normalize_punctuation()\n", "                      .remove_special_punctuation()\n", "                      .remove_punctuation(white_list=[\"-\", \".\", \"+\", \"&\"])\n", "                      .lower_case())\n", "        \n", "        # Remove extra spaces\n", "        clean_title = \" \".join(clean_title.split())\n", "        \n", "        # if self.verbose:\n", "        #     print(f\"Original: {title}\")\n", "        #     print(f\"Cleaned:  {clean_title}\")\n", "            \n", "        return clean_title\n", "        \n", "    def process_titles(self, df, title_col='title'):\n", "        \"\"\"Process all titles in a dataframe\"\"\"\n", "        df['clean_title'] = df[title_col].progress_apply(self.clean_title)\n", "        return df"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2682031/2682031 [01:43<00:00, 26018.22it/s]\n"]}], "source": ["# Process feature\n", "# Process title\n", "title_cleaner = AmazonTitleCleaner(verbose=True)\n", "df_merge_clean = title_cleaner.process_titles(df_merge_clean)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2152060, 10)\n"]}], "source": ["# Process price\n", "df_merge_clean['price_clean'] = df_merge_clean['price'].apply(lambda x: str(x).replace('$',''))\n", "df_merge_clean = df_merge_clean[df_merge_clean['price_clean']!='']\n", "print(df_merge_clean.shape)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2093073, 10)\n"]}], "source": ["# Process brand\n", "df_merge_clean = df_merge_clean[df_merge_clean['brand'] != '']\n", "print(df_merge_clean.shape)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2034303, 10)\n"]}], "source": ["# Process category\n", "df_merge_clean = df_merge_clean[df_merge_clean['category'] != '[]']\n", "print(df_merge_clean.shape)\n", "\n", "df_merge_clean['category'] = df_merge_clean['category'].apply(lambda x: ast.literal_eval(x))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["df_merge_clean['category_clean'] = df_merge_clean['category'].apply(lambda x: \"-\".join(str(i) for i in x))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df_merge_clean = df_merge_clean[['overall','reviewerID','asin','unixReviewTime_convert','clean_title','category_clean','price_clean','brand']]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["(2034303, 8)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge_clean.shape"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["df_merge_clean = df_merge_clean.sort_values(by='unixReviewTime_convert')\n", "df_merge_clean = df_merge_clean.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["df_merge_clean['label'] = df_merge_clean['overall'].apply(lambda x: 1 if x > 4 else 0)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>overall</th>\n", "      <th>reviewerID</th>\n", "      <th>asin</th>\n", "      <th>unixReviewTime_convert</th>\n", "      <th>clean_title</th>\n", "      <th>category_clean</th>\n", "      <th>price_clean</th>\n", "      <th>brand</th>\n", "      <th>label</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2034298</th>\n", "      <td>1.0</td>\n", "      <td>A1GSDJ7REHK9EY</td>\n", "      <td>B01GW2HF98</td>\n", "      <td>2018-10-02</td>\n", "      <td>hydro flask wide mouth bpa free insulated spor...</td>\n", "      <td>Sports &amp; Outdoors-Sports &amp; Fitness-Accessories...</td>\n", "      <td>12.22</td>\n", "      <td>Hydro Flask</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2034299</th>\n", "      <td>5.0</td>\n", "      <td>A13H2ZRENPV9E</td>\n", "      <td>B01HEGK5U2</td>\n", "      <td>2018-10-03</td>\n", "      <td>mossy oak men s poly camo hoodie</td>\n", "      <td>Sports &amp; Outdoors-Sports &amp; Fitness-Hunting &amp; F...</td>\n", "      <td>55.00</td>\n", "      <td>Mossy Oak</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2034300</th>\n", "      <td>5.0</td>\n", "      <td>A11AT3PC06Y5WT</td>\n", "      <td>B01HCKR9ZE</td>\n", "      <td>2018-10-03</td>\n", "      <td>adidas men s designed-2-move shorts</td>\n", "      <td>Sports &amp; Outdoors-Sports &amp; Fitness-Exercise &amp; ...</td>\n", "      <td>14.95 - 88.61</td>\n", "      <td>adidas</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2034301</th>\n", "      <td>2.0</td>\n", "      <td>A1P3ROFFUTEWKG</td>\n", "      <td>B01HHB2HK0</td>\n", "      <td>2018-10-03</td>\n", "      <td>gas one gs-3000 portable gas stove with carryi...</td>\n", "      <td>Sports &amp; Outdoors-Outdoor Recreation-Camping &amp;...</td>\n", "      <td>5.12</td>\n", "      <td>GasOne</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2034302</th>\n", "      <td>1.0</td>\n", "      <td>A30DF0WFD9U1PT</td>\n", "      <td>B01HIY7NPU</td>\n", "      <td>2018-10-04</td>\n", "      <td>ventura brake disc</td>\n", "      <td>Sports &amp; Outdoors-Outdoor Recreation-Cycling-P...</td>\n", "      <td>7.97</td>\n", "      <td>Ventura</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         overall      reviewerID        asin unixReviewTime_convert  \\\n", "2034298      1.0  A1GSDJ7REHK9EY  B01GW2HF98             2018-10-02   \n", "2034299      5.0   A13H2ZRENPV9E  B01HEGK5U2             2018-10-03   \n", "2034300      5.0  A11AT3PC06Y5WT  B01HCKR9ZE             2018-10-03   \n", "2034301      2.0  A1P3ROFFUTEWKG  B01HHB2HK0             2018-10-03   \n", "2034302      1.0  A30DF0WFD9U1PT  B01HIY7NPU             2018-10-04   \n", "\n", "                                               clean_title  \\\n", "2034298  hydro flask wide mouth bpa free insulated spor...   \n", "2034299                   mossy oak men s poly camo hoodie   \n", "2034300                adidas men s designed-2-move shorts   \n", "2034301  gas one gs-3000 portable gas stove with carryi...   \n", "2034302                                 ventura brake disc   \n", "\n", "                                            category_clean    price_clean  \\\n", "2034298  Sports & Outdoors-Sports & Fitness-Accessories...          12.22   \n", "2034299  Sports & Outdoors-Sports & Fitness-Hunting & F...          55.00   \n", "2034300  Sports & Outdoors-Sports & Fitness-Exercise & ...  14.95 - 88.61   \n", "2034301  Sports & Outdoors-Outdoor Recreation-Camping &...           5.12   \n", "2034302  Sports & Outdoors-Outdoor Recreation-Cycling-P...           7.97   \n", "\n", "               brand  label  \n", "2034298  Hydro Flask      0  \n", "2034299    Mossy Oak      1  \n", "2034300       adidas      1  \n", "2034301       GasOne      0  \n", "2034302      Ventura      0  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge_clean.tail()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["df_merge_clean.to_csv('/data/datasets/amazon/amazon_process.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Analysis"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== REVIEWS STATISTICS ===\n", "Total reviews: 2685991\n", "Unique users: 332447\n", "Unique items: 104687\n", "Rating distribution:\n", "overall\n", "1.0     105091\n", "2.0      96328\n", "3.0     198846\n", "4.0     468488\n", "5.0    1817238\n", "Name: count, dtype: int64\n"]}], "source": ["# Basic statistics\n", "print(\"=== REVIEWS STATISTICS ===\")\n", "print(f\"Total reviews: {len(reviews_df)}\")\n", "print(f\"Unique users: {reviews_df['reviewerID'].nunique()}\")\n", "print(f\"Unique items: {reviews_df['asin'].nunique()}\")\n", "print(f\"Rating distribution:\")\n", "print(reviews_df['overall'].value_counts().sort_index())"]}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 4}