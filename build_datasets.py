import pandas as pd
import os
import numpy as np

def check_movies_dataset(data):
    # Check age
    print(np.sort(data['age'].unique()))

    # Check occupation
    print(np.sort(data['occupation'].unique()))

    # Check gender
    all_genres = []
    for genre_list in data['genres_list']:
        all_genres.extend(genre_list)
    unique_genres = sorted(list(set(all_genres)))
    print("\nUnique genres in the dataset:")
    print(unique_genres)
    print(f"Total number of unique genres: {len(unique_genres)}")

    # Check gender
    print(np.sort(data['gender'].unique()))

def load_movielens(data_path):
    # Load ratings data
    ratings_cols = ['user_id', 'movie_id', 'rating', 'timestamp']
    ratings = pd.read_csv(os.path.join(data_path, 'ratings.dat'), sep='::', names=ratings_cols, encoding='ISO-8859-1', engine='python')
    ratings = ratings.drop_duplicates()

    # Load users data
    users_cols = ['user_id', 'gender', 'age', 'occupation', 'zip_code']
    users = pd.read_csv(os.path.join(data_path, 'users.dat'), sep='::', names=users_cols, encoding='ISO-8859-1', engine='python')
    users = users.drop_duplicates()

    # Load movies data
    movies_cols = ['movie_id', 'title', 'genres']
    movies = pd.read_csv(os.path.join(data_path, 'movies.dat'), sep='::', names=movies_cols, encoding='ISO-8859-1', engine='python')
    movies = movies.drop_duplicates()

    return ratings, users, movies

def standardize_zipcode(zipcode):
    """Standardize zipcode by keeping only the first 5 digits"""
    # Convert to string first in case it's numeric
    zipcode = str(zipcode)
    # Split on hyphen and take the first part
    return zipcode.split('-')[0]
def preprocess_movielens(ratings, users, movies):
    # Convert timestamp to datetime
    ratings['datetime'] = pd.to_datetime(ratings['timestamp'], unit='s')
    print(ratings[['user_id', 'movie_id', 'rating', 'datetime','timestamp']].head())

    # Split genres into a list
    movies['genres_list'] = movies['genres'].str.split('|')
    print(movies[['movie_id', 'title', 'genres_list','genres']].head())

    # Merge ratings with user information
    ratings_users = pd.merge(ratings, users, on='user_id')

    # Merge with movie information
    complete_df = pd.merge(ratings_users, movies, on='movie_id')
    print(f"\nComplete Merged Dataset: Shape: {complete_df.shape}")

    # Convert gender to index
    complete_df['gender'] = complete_df['gender'].map({'M': 1, 'F': 2})

    # Create zipcode to index mapping
    complete_df['zip_code_v2'] = complete_df['zip_code'].apply(lambda x: standardize_zipcode(x))
    # Remove invalid zipcodes
    complete_df['zipcode_len'] = complete_df['zip_code_v2'].apply(lambda x: len(x))
    complete_df = complete_df[complete_df['zipcode_len']<=5]
    unique_zipcodes = sorted(complete_df['zip_code_v2'].unique())
    zipcode_to_idx = {zipcode: idx + 1 for idx, zipcode in enumerate(unique_zipcodes)}  # Start from 1
    complete_df['zip_code_index'] = complete_df['zip_code_v2'].map(zipcode_to_idx)
    zipcode_mapping = pd.DataFrame({'zip_code_v2': list(zipcode_to_idx.keys()),'index': list(zipcode_to_idx.values())})
    zipcode_mapping.to_csv(os.path.join(os.path.dirname(data_path), 'zipcode_mapping.csv'), index=False)

    # Create genre to index mapping
    all_genres = []
    for genre_list in complete_df['genres_list']:
        all_genres.extend(genre_list)
    unique_genres = sorted(list(set(all_genres)))
    genre_to_idx = {genre: idx + 1 for idx, genre in enumerate(unique_genres)} 
    # Start from 1, 0 can be reserved for padding
    # Convert genres to indices
    complete_df['genres_index'] = complete_df['genres_list'].apply(lambda x: [genre_to_idx[genre] for genre in x])
    # Save genre mapping
    genre_mapping = pd.DataFrame({'genre': list(genre_to_idx.keys()),'index': list(genre_to_idx.values())})
    genre_mapping.to_csv(os.path.join(os.path.dirname(data_path), 'genre_mapping.csv'), index=False)

    # remove movies with only one rating
    movie_num = pd.DataFrame({'movie_id': complete_df['movie_id'].value_counts().index, 'count': complete_df['movie_id'].value_counts().values})
    movie_num = movie_num.sort_values(by='count', ascending=False)
    complete_df_v2 = complete_df[complete_df['movie_id'].isin(movie_num[movie_num['count'] > 1]['movie_id'])]
    print('Removed movies with only one rating', complete_df_v2.shape)

    return complete_df_v2

def process_movielens_label(data):
    """Process MovieLens ratings into binary labels:
    - Remove neutral ratings (rating = 3)
    - Convert high ratings (> 3) to clicks (1)
    - Convert low ratings (< 3) to no clicks (0)
    """
    # Remove neutral ratings (rating = 3)
    data = data[data['rating'] != 3].reset_index(drop=True)
    
    # Convert ratings to binary labels
    data['label'] = data['rating'].apply(lambda x: 1 if x > 3 else 0)
    
    # Print statistics
    total_samples = len(data)
    click_samples = (data['label'] == 1).sum()
    no_click_samples = (data['label'] == 0).sum()
    
    print("\nLabel Distribution after processing:")
    print(f"Total samples: {total_samples}")
    print(f"Click (rating > 3): {click_samples} ({click_samples/total_samples*100:.1f}%)")
    print(f"No click (rating < 3): {no_click_samples} ({no_click_samples/total_samples*100:.1f}%)")
    
    return data

def split_movielens(data):
    # Sort all data by datetime
    sorted_data = data.sort_values('datetime').reset_index(drop=True)
    
    # Initialize empty DataFrames for train/val/test
    train_data = []
    val_data = []
    test_data = []
    
    # Group by user_id and process each user's interactions
    for user_id, user_data in sorted_data.groupby('user_id'):
        # Sort user's interactions by time
        user_data = user_data.sort_values('datetime')
        n_interactions = len(user_data)
        
        # Calculate split points to achieve roughly 80:10:10
        n_train = max(1, int(0.8 * n_interactions))  # At least 1 for training
        n_val = max(0, int(0.1 * n_interactions))    # Could be 0 for very few interactions
        
        if n_interactions >= 10:  # Enough interactions for proper split
            train_data.append(user_data.iloc[:n_train])
            if n_val > 0:
                val_data.append(user_data.iloc[n_train:n_train+n_val])
            test_data.append(user_data.iloc[n_train+n_val:])
        elif n_interactions >= 3:  # Moderate number of interactions
            train_data.append(user_data.iloc[:-2])
            val_data.append(user_data.iloc[-2:-1])
            test_data.append(user_data.iloc[-1:])
        elif n_interactions == 2:  # Only two interactions
            train_data.append(user_data.iloc[:1])
            val_data.append(user_data.iloc[1:])
        else:  # Single interaction
            train_data.append(user_data)
    
    # Combine all data
    train_data = pd.concat(train_data, ignore_index=True)
    val_data = pd.concat(val_data, ignore_index=True)
    test_data = pd.concat(test_data, ignore_index=True)
    
    # Get training movies set, user set
    train_users = set(train_data['user_id'].unique())
    train_movies = set(train_data['movie_id'].unique())
    
    # Remove validation and test instances where movie_id is not in training
    val_data = val_data[val_data['movie_id'].isin(train_movies)]
    test_data = test_data[test_data['movie_id'].isin(train_movies)]
    # Filter val/test users as well
    val_data = val_data[val_data['user_id'].isin(train_users)]
    test_data = test_data[test_data['user_id'].isin(train_users)]
    
    # Verify overlap after filtering
    train_users = set(train_data['user_id'].unique())
    val_users = set(val_data['user_id'].unique())
    test_users = set(test_data['user_id'].unique())
    
    val_movies = set(val_data['movie_id'].unique())
    test_movies = set(test_data['movie_id'].unique())

    # Check for 100% coverage
    val_user_coverage = len(val_users & train_users) / len(val_users) if len(val_users) > 0 else 1.0
    test_user_coverage = len(test_users & train_users) / len(test_users) if len(test_users) > 0 else 1.0
    val_movie_coverage = len(val_movies & train_movies) / len(val_movies) if len(val_movies) > 0 else 1.0
    test_movie_coverage = len(test_movies & train_movies) / len(test_movies) if len(test_movies) > 0 else 1.0
    
    if not all(x == 1.0 for x in [val_user_coverage, test_user_coverage, val_movie_coverage, test_movie_coverage]):
        raise ValueError(
            "Incomplete coverage detected:\n"
            f"Val users coverage: {val_user_coverage*100:.1f}%\n"
            f"Test users coverage: {test_user_coverage*100:.1f}%\n"
            f"Val movies coverage: {val_movie_coverage*100:.1f}%\n"
            f"Test movies coverage: {test_movie_coverage*100:.1f}%"
        )
    
    total_samples = len(train_data) + len(val_data) + len(test_data)
    
    # Print statistics
    print("\nDataset Sizes:")
    print(f"Training set: {len(train_data)} samples ({len(train_data)/total_samples*100:.1f}%)")
    print(f"Validation set: {len(val_data)} samples ({len(val_data)/total_samples*100:.1f}%)")
    print(f"Testing set: {len(test_data)} samples ({len(test_data)/total_samples*100:.1f}%)")

    return train_data, val_data, test_data

if __name__ == "__main__":
    # Define the path to the dataset
    data_path = '/data/datasets/movielens'
    save_path = '/data/datasets/processed_datasets/movielens'

    # Load the datasets
    ratings, users, movies = load_movielens(data_path)

    # Display basic information about the dataframes
    print(f"Ratings DataFrame Shape: {ratings.shape}")
    print(f"\nUsers DataFrame Shape: {users.shape}")
    print(f"\nMovies DataFrame: Shape: {movies.shape}")

    # Merge data and preprocess
    complete_df = preprocess_movielens(ratings, users, movies)
    # Convert label
    complete_df = process_movielens_label(complete_df)

    # Split the dataset
    train_data, val_data, test_data = split_movielens(complete_df)
    # Save the datasets
    train_data.to_csv(os.path.join(save_path, 'train.csv'), index=False)
    val_data.to_csv(os.path.join(save_path, 'val.csv'), index=False)
    test_data.to_csv(os.path.join(save_path, 'test.csv'), index=False)
    print("Datasets saved successfully!")



