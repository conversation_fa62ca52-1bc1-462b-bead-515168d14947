import pandas as pd
import os
import numpy as np
import sys
from io import StringIO
import re

def load_bookcrossing(data_path):
    """
    Load the Book Crossing dataset files.
    """
    try:
        # Load ratings data
        ratings_path = os.path.join(data_path, 'BX-Book-Ratings.csv')
        print(f"Loading ratings from {ratings_path}")
        ratings = pd.read_csv(
            ratings_path,
            sep=';',
            encoding='ISO-8859-1',
            quotechar='"',
            escapechar='\\'
        )
        ratings = ratings.drop_duplicates()

        # Load users data
        users_path = os.path.join(data_path, 'BX-Users.csv')
        print(f"Loading users from {users_path}")
        users = pd.read_csv(
            users_path,
            sep=';',
            encoding='ISO-8859-1',
            quotechar='"',
            escapechar='\\'
        )
        users = users.drop_duplicates()

        # Load books data
        books_path = os.path.join(data_path, 'BX_Books.csv')
        print(f"Loading books from {books_path}")
        books = pd.read_csv(
            books_path,
            sep=';',
            encoding='ISO-8859-1',
            quotechar='"',
            escapechar='\\'
        )
        books = books.drop_duplicates()
        # remove URL
        books = books.drop(columns=['Image-URL-S', 'Image-URL-M', 'Image-URL-L'])

        return ratings, users, books

    except:
        raise Exception("Error loading Book Crossing dataset")

def clean_dirty(city: str) -> str:
    city = str(city).strip().lower()

    city = re.sub(r'^[\W_]+|[\W_]+$', '', city)

    if not city or re.fullmatch(r'[\W_]+', city):
        return 'unknown'

    return city.strip()

def clean_dirty_num(entry: str) -> str:
        entry = str(entry).strip().lower()

        if re.fullmatch(r'\d+', entry):
            return 'unknown'

        entry = re.sub(r'^\d+\s*', '', entry)

        if not entry or re.fullmatch(r'[\W_]+', entry):
            return 'unknown'

        return entry.strip()

def preprocess_bookcrossing(ratings, users, books):
    """
    Preprocess the Book Crossing dataset.
    """
    print("Starting dataset cleaning...")

    # Print initial dataset sizes
    print(f"Initial ratings: {len(ratings)} rows")
    print(f"Initial users: {len(users)} rows")
    print(f"Initial books: {len(books)} rows")

    # Rename columns to match our expected format
    ratings = ratings.rename(columns={
        'User-ID': 'user_id',
        'ISBN': 'item_id',
        'Book-Rating': 'rating'
    })

    users = users.rename(columns={
        'User-ID': 'user_id',
        'Location': 'location',
        'Age': 'age'
    })

    books = books.rename(columns={
        'ISBN': 'item_id',
        'Book-Title': 'title',
        'Book-Author': 'author',
        'Year-Of-Publication': 'year',
        'Publisher': 'publisher'
    })

    # Convert data types for ratings
    ratings['user_id'] = ratings['user_id'].astype(int)
    ratings['rating'] = ratings['rating'].astype(int)

    # Handle age values
    users['age'] = pd.to_numeric(users['age'], errors='coerce')

    # Count missing ages before cleaning
    missing_age_before = users['age'].isna().sum()

    # Set abnormal age values to NaN (age < 5 or age > 100)
    abnormal_age_count = ((users.age < 5) | (users.age > 100)).sum()
    users.loc[(users.age < 5) | (users.age > 100), 'age'] = np.nan
    print(f"Set {abnormal_age_count} abnormal age values to NaN")

    # Replace NaN ages with -1 as per requirements
    users['age'] = users['age'].fillna(-1).astype(int)
    print(f"Replaced {missing_age_before} missing age values with -1")

    # Clean location data
    print("\nCleaning location data...")
    # Split location into city, state, and country
    users['location'] = users['location'].astype(str)
    users['location'] = users['location'].apply(lambda x: x.strip())
    user_location_expanded = users['location'].str.split(pat=',', n=2, expand=True)
    user_location_expanded.columns = ['city', 'state', 'country']
    users = users.join(user_location_expanded)
    # Remove space and clean quotes/HTML entities
    for col in user_location_expanded.columns:
        users[col] = users[col].str.strip()
        # Remove multiple quotes and HTML entities
        users[col] = users[col].str.replace(r'^"+|"+$', '', regex=True)  # Remove leading/trailing quotes
        users[col] = users[col].str.replace(r'&[#\w]+;', '', regex=True)  # Remove HTML entities
        users[col] = users[col].str.replace(r'["\']', '', regex=True)  # Remove remaining quotes
        users[col] = users[col].str.replace(r'\\', '', regex=True)  # Remove backslashes
        users[col] = users[col].str.replace(r'\s+', ' ', regex=True)  # Remove extra spaces
        users[col] = users[col].str.lstrip(',').str.strip()
    # Lowercase all columns
    for col in user_location_expanded.columns:
        users[col] = users[col].str.lower()

    # Replace NaN and empty values with 'unknown'
    for col in ['city', 'state', 'country']:
        users[col] = users[col].fillna('unknown')
        users[col] = users[col].replace('', 'unknown')
        users[col] = users[col].replace(',', 'unknown')
        users[col] = users[col].apply(clean_dirty)
        users[col] = users[col].apply(clean_dirty_num)
        empty_count = (users[col] == 'unknown').sum()
        print(f'replaced empty {col}: {empty_count}')

    # Convert year to numeric and clean it first
    books.year = pd.to_numeric(books.year, errors='coerce')
    zero_year_count = (books.year == 0).sum()
    books.year = books.year.replace(0, -1)
    print(f"Replace {zero_year_count} zero year values with -1")

    # Clean text fields from quotes and HTML entities BEFORE creating mappings
    print("\nCleaning text fields...")
    for col in ['author', 'publisher', 'title']:
        books[col] = books[col].astype(str)
        books[col] = books[col].str.replace(r'^"+|"+$', '', regex=True)  # Remove leading/trailing quotes
        books[col] = books[col].str.replace(r'&[#\w]+;', '', regex=True)  # Remove HTML entities
        books[col] = books[col].str.replace(r'["\']', '', regex=True)  # Remove remaining quotes
        books[col] = books[col].str.strip()  # Remove extra spaces

    # Remove nan values
    books_author_nan = len(books[books['author'].isna()])
    books = books[~books['author'].isna()]
    print(f"Remove {books_author_nan} nan author values")

    books_publisher_nan = len(books[books['publisher'].isna()])
    books = books[~books['publisher'].isna()]
    print(f"Remove {books_publisher_nan} nan publisher values")

    books_title_nan = len(books[books['title'].isna()])
    books = books[~books['title'].isna()]
    print(f"Remove {books_title_nan} nan title values")

    # Drop the original location column
    users.drop(columns=['location'], inplace=True)

    # NOW create indices for categorical features AFTER cleaning (save to raw data directory)
    print("\nCreating indices for categorical features...")

    # Create city index (0-based indexing)
    unique_cities = sorted(users['city'].unique())
    city_to_idx = {city: idx for idx, city in enumerate(unique_cities)}  # Start from 0
    users['city_index'] = users['city'].map(city_to_idx)
    city_mapping = pd.DataFrame({'city': list(city_to_idx.keys()), 'index': list(city_to_idx.values())})
    city_mapping.to_csv(os.path.join(data_path, 'city_mapping.csv'), index=False)

    # Create state index (0-based indexing)
    unique_states = sorted(users['state'].unique())
    state_to_idx = {state: idx for idx, state in enumerate(unique_states)}  # Start from 0
    users['state_index'] = users['state'].map(state_to_idx)
    state_mapping = pd.DataFrame({'state': list(state_to_idx.keys()), 'index': list(state_to_idx.values())})
    state_mapping.to_csv(os.path.join(data_path, 'state_mapping.csv'), index=False)

    # Create country index (0-based indexing)
    unique_countries = sorted(users['country'].unique())
    country_to_idx = {country: idx for idx, country in enumerate(unique_countries)}  # Start from 0
    users['country_index'] = users['country'].map(country_to_idx)
    country_mapping = pd.DataFrame({'country': list(country_to_idx.keys()), 'index': list(country_to_idx.values())})
    country_mapping.to_csv(os.path.join(data_path, 'country_mapping.csv'), index=False)

    # Create author index (0-based indexing)
    unique_authors = sorted(books['author'].fillna('unknown').unique())
    author_to_idx = {author: idx for idx, author in enumerate(unique_authors)}  # Start from 0
    books['author_index'] = books['author'].fillna('unknown').map(author_to_idx)
    author_mapping = pd.DataFrame({'author': list(author_to_idx.keys()), 'index': list(author_to_idx.values())})
    author_mapping.to_csv(os.path.join(data_path, 'author_mapping.csv'), index=False)

    # Create publisher index (0-based indexing)
    unique_publishers = sorted(books['publisher'].fillna('unknown').unique())
    publisher_to_idx = {publisher: idx for idx, publisher in enumerate(unique_publishers)}  # Start from 0
    books['publisher_index'] = books['publisher'].fillna('unknown').map(publisher_to_idx)
    publisher_mapping = pd.DataFrame({'publisher': list(publisher_to_idx.keys()), 'index': list(publisher_to_idx.values())})
    publisher_mapping.to_csv(os.path.join(data_path, 'publisher_mapping.csv'), index=False)

    # Print final dataset sizes after cleaning
    print("\nFinal dataset sizes after cleaning:")
    print(f"Ratings: {len(ratings)} rows")
    print(f"Users: {len(users)} rows")
    print(f"Books: {len(books)} rows")

    return ratings, users, books

def copy_other_mappings_to_processed_dir(save_path):
    """
    Copy other mapping files (not user/item) to the processed dataset directory for pipeline compatibility
    """
    import shutil

    # Create id_mappings directory in processed dataset
    id_mappings_dir = os.path.join(save_path, 'id_mappings')
    os.makedirs(id_mappings_dir, exist_ok=True)

    # Copy other mapping files from raw data directory to processed directory
    # Note: user_id and item_id mappings are created separately in create_final_mappings
    raw_data_dir = '/data/datasets/bookcrossing'
    mapping_files = [
        'city_mapping.csv',
        'state_mapping.csv',
        'country_mapping.csv',
        'author_mapping.csv',
        'publisher_mapping.csv'
    ]

    for mapping_file in mapping_files:
        src_path = os.path.join(raw_data_dir, mapping_file)
        dst_path = os.path.join(id_mappings_dir, mapping_file)
        if os.path.exists(src_path):
            shutil.copy2(src_path, dst_path)
            print(f"Copied {mapping_file} to {id_mappings_dir}")

    print(f"Other mapping files saved to {id_mappings_dir}")

def create_final_mappings(train_data, val_data, test_data, save_path):
    """
    Create user/item mappings based on the final train/val/test data to ensure consistency
    """
    print("\nCreating final user/item mappings...")

    # Get all users and items from train/val/test data
    all_data = pd.concat([train_data, val_data, test_data], ignore_index=True)

    # Create user_id mapping (0-based indexing for sparse tensor compatibility)
    unique_users = sorted(all_data['user_id'].unique())
    user_to_idx = {user: idx for idx, user in enumerate(unique_users)}  # Start from 0
    user_mapping = pd.DataFrame({'user_id': list(user_to_idx.keys()), 'index': list(user_to_idx.values())})

    # Create item_id mapping (0-based indexing for sparse tensor compatibility)
    unique_items = sorted(all_data['item_id'].unique())
    item_to_idx = {item: idx for idx, item in enumerate(unique_items)}  # Start from 0
    item_mapping = pd.DataFrame({'item_id': list(item_to_idx.keys()), 'index': list(item_to_idx.values())})

    # Add index columns to the data
    all_data['user_index'] = all_data['user_id'].map(user_to_idx)
    all_data['item_index'] = all_data['item_id'].map(item_to_idx)

    # Split back into train/val/test with updated indices
    train_len = len(train_data)
    val_len = len(val_data)

    updated_train = all_data.iloc[:train_len].copy()
    updated_val = all_data.iloc[train_len:train_len+val_len].copy()
    updated_test = all_data.iloc[train_len+val_len:].copy()

    # Save user/item mappings to processed directory (like MovieLens)
    id_mappings_dir = os.path.join(save_path, 'id_mappings')
    os.makedirs(id_mappings_dir, exist_ok=True)

    user_mapping.to_csv(os.path.join(id_mappings_dir, 'user_id_mapping.csv'), index=False)
    item_mapping.to_csv(os.path.join(id_mappings_dir, 'item_id_mapping.csv'), index=False)

    print(f"Created mappings: {len(unique_users)} users, {len(unique_items)} items")
    print(f"Saved user/item mappings to {id_mappings_dir}")

    return updated_train, updated_val, updated_test

def merge_bookcrossing_data(ratings, users, books):
    """
    Merge the cleaned ratings, users, and books dataframes into a complete dataset.
    """
    print("\nMerging datasets...")

    # First merge ratings with users
    print("Merging ratings with users...")
    ratings['user_id'] = ratings['user_id'].astype(int)
    users['user_id'] = users['user_id'].astype(int)
    ratings_users = pd.merge(ratings, users, on='user_id', how='inner')
    print(f"Combined ratings-users shape: {ratings_users.shape}")

    # Then merge with books
    print("Merging with books...")
    ratings_users['item_id'] = ratings_users['item_id'].astype(str)
    books['item_id'] = books['item_id'].astype(str)
    complete_df = pd.merge(ratings_users, books, on='item_id', how='inner')
    print(f"Final merged dataset shape: {complete_df.shape}")

    return complete_df

def process_bookcrossing_label(data):
    """
    Process Book Crossing ratings into binary labels:
    - Convert high ratings (> 5) to positive (1)
    - Convert low ratings (≤ 5) to negative (0)
    """
    # Convert ratings to binary labels
    data['label'] = data['rating'].apply(lambda x: 1 if x > 5 else 0)

    # Print statistics
    total_samples = len(data)
    positive_samples = (data['label'] == 1).sum()
    negative_samples = (data['label'] == 0).sum()

    print("\nLabel Distribution after processing:")
    print(f"Total samples: {total_samples}")
    print(f"Positive (rating > 5): {positive_samples} ({positive_samples/total_samples*100:.1f}%)")
    print(f"Negative (rating ≤ 5): {negative_samples} ({negative_samples/total_samples*100:.1f}%)")

    return data

def split_bookcrossing(data):
    """
    Split the Book Crossing dataset into train, validation, and test sets.
    Uses a simple approach without relying on timestamps.

    For users with single interactions, distributes them across train/val/test
    with an 80/10/10 split, while ensuring all items in val/test also appear
    in the training set.
    """
    print("\nSplitting dataset...")

    # Set random seed for reproducibility
    np.random.seed(42)

    # Initialize empty lists for train/val/test
    train_data = []
    val_data = []
    test_data = []

    # First, separate users by number of interactions
    user_groups = data.groupby('user_id')
    total_users = len(user_groups)
    print(f"Total users to process: {total_users}")

    # Create separate lists for different user types
    users_with_1_interaction = []
    users_with_2_interactions = []
    users_with_3_to_9_interactions = []
    users_with_10plus_interactions = []

    # Categorize users by interaction count
    for user_id, user_data in user_groups:
        n_interactions = len(user_data)
        if n_interactions == 1:
            users_with_1_interaction.append((user_id, user_data))
        elif n_interactions == 2:
            users_with_2_interactions.append((user_id, user_data))
        elif n_interactions >= 3 and n_interactions < 10:
            users_with_3_to_9_interactions.append((user_id, user_data))
        else:  # n_interactions >= 10
            users_with_10plus_interactions.append((user_id, user_data))

    # Print user interaction statistics
    print("\nUser interaction statistics:")
    print(f"Users with 1 interaction: {len(users_with_1_interaction)} ({len(users_with_1_interaction)/total_users*100:.1f}%)")
    print(f"Users with 2 interactions: {len(users_with_2_interactions)} ({len(users_with_2_interactions)/total_users*100:.1f}%)")
    print(f"Users with 3-9 interactions: {len(users_with_3_to_9_interactions)} ({len(users_with_3_to_9_interactions)/total_users*100:.1f}%)")
    print(f"Users with 10+ interactions: {len(users_with_10plus_interactions)} ({len(users_with_10plus_interactions)/total_users*100:.1f}%)")

    # Process users with multiple interactions first
    print("\nProcessing users with multiple interactions...")

    # Process users with 10+ interactions
    for user_id, user_data in users_with_10plus_interactions:
        # Shuffle the user's interactions to randomize the split
        user_data = user_data.sample(frac=1, random_state=42).reset_index(drop=True)
        n_interactions = len(user_data)

        # Calculate split points to achieve roughly 80:10:10
        n_train = max(1, int(0.8 * n_interactions))
        n_val = max(0, int(0.1 * n_interactions))

        train_data.append(user_data.iloc[:n_train])
        if n_val > 0:
            val_data.append(user_data.iloc[n_train:n_train+n_val])
        test_data.append(user_data.iloc[n_train+n_val:])

    # Process users with 3-9 interactions
    for user_id, user_data in users_with_3_to_9_interactions:
        user_data = user_data.sample(frac=1, random_state=42).reset_index(drop=True)
        train_data.append(user_data.iloc[:-2])
        val_data.append(user_data.iloc[-2:-1])
        test_data.append(user_data.iloc[-1:])

    # Process users with 2 interactions
    for user_id, user_data in users_with_2_interactions:
        user_data = user_data.sample(frac=1, random_state=42).reset_index(drop=True)
        train_data.append(user_data.iloc[:1])
        val_data.append(user_data.iloc[1:])

    # Combine data from multi-interaction users
    print("Combining data from multi-interaction users...")
    train_multi = pd.concat(train_data, ignore_index=True) if train_data else pd.DataFrame()
    val_multi = pd.concat(val_data, ignore_index=True) if val_data else pd.DataFrame()
    test_multi = pd.concat(test_data, ignore_index=True) if test_data else pd.DataFrame()

    # Get item sets from multi-interaction users
    train_items = set(train_multi['item_id'].unique())

    # Now process users with single interactions
    print("\nProcessing users with single interactions...")

    # Shuffle the single-interaction users for random assignment
    np.random.shuffle(users_with_1_interaction)

    # Calculate split points for single-interaction users
    n_single_users = len(users_with_1_interaction)
    n_train_single = int(0.8 * n_single_users)
    n_val_single = int(0.1 * n_single_users)

    # Initialize lists for single-interaction users
    train_single = []
    val_single = []
    test_single = []

    # First, identify items that must be in training set
    # These are items that only appear in single-interaction records
    all_items = set(data['item_id'].unique())
    items_not_in_train = all_items - train_items

    # Find single-interaction records with these items
    critical_items = {}
    for user_id, user_data in users_with_1_interaction:
        item_id = user_data['item_id'].iloc[0]
        if item_id in items_not_in_train:
            if item_id not in critical_items:
                critical_items[item_id] = []
            critical_items[item_id].append((user_id, user_data))

    # Ensure at least one record for each critical item goes to training
    critical_users = set()
    for item_id, users in critical_items.items():
        if users:  # If there are users with this item
            # Take the first user with this item for training
            critical_users.add(users[0][0])
            train_single.append(users[0][1])
            # Add this item to training items
            train_items.add(item_id)

    # Now distribute remaining single-interaction users
    train_count = len(train_single)
    val_count = 0
    test_count = 0

    for user_id, user_data in users_with_1_interaction:
        if user_id in critical_users:
            continue  # Skip users already assigned

        item_id = user_data['item_id'].iloc[0]

        # If item not in training items, add to training
        if item_id not in train_items:
            train_single.append(user_data)
            train_items.add(item_id)
            train_count += 1
        # Otherwise, distribute according to target ratios
        elif train_count < n_train_single:
            train_single.append(user_data)
            train_count += 1
        elif val_count < n_val_single:
            val_single.append(user_data)
            val_count += 1
        else:
            test_single.append(user_data)
            test_count += 1

    print(f"Single-interaction distribution - Train: {train_count}, Val: {val_count}, Test: {test_count}")

    # Combine single-interaction data
    train_single = pd.concat(train_single, ignore_index=True) if train_single else pd.DataFrame()
    val_single = pd.concat(val_single, ignore_index=True) if val_single else pd.DataFrame()
    test_single = pd.concat(test_single, ignore_index=True) if test_single else pd.DataFrame()

    # Combine all data
    print("\nCombining all split data...")
    train_data = pd.concat([train_multi, train_single], ignore_index=True) if not train_multi.empty or not train_single.empty else pd.DataFrame()
    val_data = pd.concat([val_multi, val_single], ignore_index=True) if not val_multi.empty or not val_single.empty else pd.DataFrame()
    test_data = pd.concat([test_multi, test_single], ignore_index=True) if not test_multi.empty or not test_single.empty else pd.DataFrame()

    print(f"Initial split sizes - Train: {len(train_data)}, Val: {len(val_data)}, Test: {len(test_data)}")

    # Get training items and user sets
    train_users = set(train_data['user_id'].unique())
    train_items = set(train_data['item_id'].unique())

    print(f"Unique users in training: {len(train_users)}")
    print(f"Unique items in training: {len(train_items)}")

    # Double-check validation and test sets
    print("\nVerifying validation and test sets...")

    # Count before verification
    val_before = len(val_data)
    test_before = len(test_data)

    # Create cold-start datasets
    # First identify cold-start items and users
    cold_start_items = set(val_data['item_id'].unique()) - train_items
    cold_start_items.update(set(test_data['item_id'].unique()) - train_items)
    cold_start_users = set(val_data['user_id'].unique()) - train_users
    cold_start_users.update(set(test_data['user_id'].unique()) - train_users)

    print(f"Found {len(cold_start_items)} cold-start items")
    print(f"Found {len(cold_start_users)} cold-start users")

    # Extract cold-start interactions
    cold_start_val_item = val_data[~val_data['item_id'].isin(train_items)].copy()
    cold_start_test_item = test_data[~test_data['item_id'].isin(train_items)].copy()

    # After removing cold-start items, check for cold-start users
    val_data_items_filtered = val_data[val_data['item_id'].isin(train_items)]
    test_data_items_filtered = test_data[test_data['item_id'].isin(train_items)]

    cold_start_val_user = val_data_items_filtered[~val_data_items_filtered['user_id'].isin(train_users)].copy()
    cold_start_test_user = test_data_items_filtered[~test_data_items_filtered['user_id'].isin(train_users)].copy()

    # Add cold-start type label
    cold_start_val_item['cold_start_type'] = 'item'
    cold_start_test_item['cold_start_type'] = 'item'
    cold_start_val_user['cold_start_type'] = 'user'
    cold_start_test_user['cold_start_type'] = 'user'

    # Combine cold-start datasets
    cold_start_val = pd.concat([cold_start_val_item, cold_start_val_user], ignore_index=True)
    cold_start_test = pd.concat([cold_start_test_item, cold_start_test_user], ignore_index=True)

    # Filter validation and test sets
    val_data = val_data_items_filtered[val_data_items_filtered['user_id'].isin(train_users)]
    test_data = test_data_items_filtered[test_data_items_filtered['user_id'].isin(train_users)]

    # Count after verification
    val_removed = val_before - len(val_data)
    test_removed = test_before - len(test_data)

    print(f"Extracted {len(cold_start_val)} validation cold-start samples")
    print(f"Extracted {len(cold_start_test)} test cold-start samples")
    print(f"Removed {val_removed} validation samples ({val_removed/val_before*100:.1f}% of validation set)")
    print(f"Removed {test_removed} test samples ({test_removed/test_before*100:.1f}% of test set)")

    # Verify coverage
    val_users = set(val_data['user_id'].unique())
    test_users = set(test_data['user_id'].unique())
    val_items = set(val_data['item_id'].unique())
    test_items = set(test_data['item_id'].unique())

    # Check for 100% coverage
    val_user_coverage = len(val_users & train_users) / len(val_users) if len(val_users) > 0 else 1.0
    test_user_coverage = len(test_users & train_users) / len(test_users) if len(test_users) > 0 else 1.0
    val_item_coverage = len(val_items & train_items) / len(val_items) if len(val_items) > 0 else 1.0
    test_item_coverage = len(test_items & train_items) / len(test_items) if len(test_items) > 0 else 1.0

    print("\nCoverage check:")
    print(f"Val users coverage: {val_user_coverage*100:.1f}%")
    print(f"Test users coverage: {test_user_coverage*100:.1f}%")
    print(f"Val items coverage: {val_item_coverage*100:.1f}%")
    print(f"Test items coverage: {test_item_coverage*100:.1f}%")

    if not all(x == 1.0 for x in [val_user_coverage, test_user_coverage, val_item_coverage, test_item_coverage]):
        raise ValueError(
            "Incomplete coverage detected:\n"
            f"Val users coverage: {val_user_coverage*100:.1f}%\n"
            f"Test users coverage: {test_user_coverage*100:.1f}%\n"
            f"Val items coverage: {val_item_coverage*100:.1f}%\n"
            f"Test items coverage: {test_item_coverage*100:.1f}%"
        )

    total_samples = len(train_data) + len(val_data) + len(test_data)

    # Print final statistics
    print("\nFinal Dataset Sizes:")
    print(f"Training set: {len(train_data)} samples ({len(train_data)/total_samples*100:.1f}%)")
    print(f"Validation set: {len(val_data)} samples ({len(val_data)/total_samples*100:.1f}%)")
    print(f"Testing set: {len(test_data)} samples ({len(test_data)/total_samples*100:.1f}%)")

    # Print final statistics
    print("\nFinal Dataset Sizes:")
    print(f"Training set: {len(train_data)} samples")
    print(f"Validation set: {len(val_data)} samples")
    print(f"Testing set: {len(test_data)} samples")
    print(f"Cold-start validation set: {len(cold_start_val)} samples")
    print(f"Cold-start testing set: {len(cold_start_test)} samples")

    return train_data, val_data, test_data, cold_start_val, cold_start_test

if __name__ == "__main__":
    # Define the path to the dataset
    data_path = '/data/datasets/bookcrossing'
    save_path = '/data/datasets/processed_datasets/bookcrossing'
    os.makedirs(save_path, exist_ok=True)

    # Load the datasets
    print("Loading BookCrossing dataset...")
    ratings, users, books = load_bookcrossing(data_path)

    # Display basic information about the dataframes
    print(f"Ratings DataFrame Shape: {ratings.shape}")
    print(f"Users DataFrame Shape: {users.shape}")
    print(f"Books DataFrame Shape: {books.shape}")

    # Clean the datasets
    ratings, users, books = preprocess_bookcrossing(ratings, users, books)

    # Merge the cleaned datasets
    complete_df = merge_bookcrossing_data(ratings, users, books)

    # Convert label
    print("\nProcessing labels...")
    complete_df = process_bookcrossing_label(complete_df)

    # Save the complete dataset
    complete_df.to_csv(os.path.join(save_path, 'complete_dataset.csv'), index=False)
    print("Complete dataset saved successfully!")

    # Split the dataset
    print("\nSplitting dataset into train/val/test sets...")
    train_data, val_data, test_data, cold_start_val, cold_start_test = split_bookcrossing(complete_df)

    # Create final mappings based on actual train/val/test data
    train_data, val_data, test_data = create_final_mappings(train_data, val_data, test_data, save_path)

    # Copy other mapping files to processed directory
    print("\nCopying other mapping files to processed directory...")
    copy_other_mappings_to_processed_dir(save_path)

    # Save the split datasets
    print("\nSaving train/val/test datasets...")
    train_data.to_csv(os.path.join(save_path, 'train.csv'), index=False)
    val_data.to_csv(os.path.join(save_path, 'val.csv'), index=False)
    test_data.to_csv(os.path.join(save_path, 'test.csv'), index=False)

    # Save cold-start datasets
    cold_start_val.to_csv(os.path.join(save_path, 'cold_start_val.csv'), index=False)
    cold_start_test.to_csv(os.path.join(save_path, 'cold_start_test.csv'), index=False)
    print("All datasets saved successfully!")
