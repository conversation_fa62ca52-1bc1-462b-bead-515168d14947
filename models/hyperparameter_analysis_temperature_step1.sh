#!/usr/bin/env bash

# Script to run contrastive learning with different temperature values
DATA_DIR="/data/datasets/processed_datasets/bookcrossing"
OUTPUT_DIR="/data/datasets/processed_datasets/bookcrossing/temperature_analysis"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BASE_OUTPUT_DIR="${OUTPUT_DIR}/temp_analysis_${TIMESTAMP}"
BATCH_SIZE=128
NUM_EPOCHS=1
LEARNING_RATE=5e-4
DEVICE="cuda"
WANDB_PROJECT="GraphLLM4CTR_Hyperparameter_Temperature_Bookcrossing_contrastive"
DATASET_TYPE="bookcrossing"

# Create base output directory
mkdir -p "${BASE_OUTPUT_DIR}"

# Log file
LOG_FILE="${BASE_OUTPUT_DIR}/temperature_analysis.log"
touch "${LOG_FILE}"

# Temperature values to analyze
TEMPERATURES=(0.5)

# Run for each temperature
for TEMP in "${TEMPERATURES[@]}"; do
    echo "=========================================="
    echo "Analyzing Temperature: ${TEMP}"
    echo "=========================================="

    # Create directory for this temperature
    TEMP_DIR="${BASE_OUTPUT_DIR}/temp_${TEMP}"
    mkdir -p "${TEMP_DIR}"

    # Run contrastive learning
    python contrastive_learning_train_for_check.py \
        --data_dir "${DATA_DIR}" \
        --model_save_dir "${TEMP_DIR}" \
        --batch_size "${BATCH_SIZE}" \
        --num_epochs "${NUM_EPOCHS}" \
        --learning_rate "${LEARNING_RATE}" \
        --device "${DEVICE}" \
        --wandb_project "${WANDB_PROJECT}" \
        --temperature "${TEMP}" \
        --dataset_type "${DATASET_TYPE}" 2>&1 | tee -a "${LOG_FILE}"
done
