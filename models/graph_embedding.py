import torch
import torch.nn as nn
from torch_geometric.nn import Node2Vec
import pandas as pd
import numpy as np
import os
import pickle
from tqdm import tqdm

class GraphEmbedder:
    def __init__(self, data_dir, dataset_type='movielens', embedding_dim=128, walk_length=20, context_size=10, walks_per_node=10,
                 p=1.0, q=1.0, num_negative_samples=1, device=torch.device("cuda" if torch.cuda.is_available() else "cpu")):
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.embedding_dim = embedding_dim
        self.walk_length = walk_length
        self.context_size = context_size
        self.walks_per_node = walks_per_node
        self.p = p
        self.q = q
        self.num_negative_samples = num_negative_samples
        self.device = device
        self.models = {}
        self.embeddings = {}
        self.id_mappings = {}

        self.load_graph_id_mappings()

    def load_graph_id_mappings(self):
        graph_dir = os.path.join(self.data_dir, 'graph')
        mappings_path = os.path.join(graph_dir, 'id_mappings.pkl')
        
        if os.path.exists(mappings_path):
            with open(mappings_path, 'rb') as f:
                mappings = pickle.load(f)
            
            if self.dataset_type == 'movielens':
                self.user_to_idx = mappings['user_to_idx']
                self.item_to_idx = mappings['item_to_idx']
                self.user_ids = mappings['user_ids']
                self.item_ids = mappings['item_ids']
            elif self.dataset_type == 'bookcrossing':
                self.user_to_idx = mappings['user_to_idx']
                self.item_to_idx = mappings['item_to_idx']
                self.user_ids = mappings['user_ids']
                self.item_ids = mappings['item_ids']
            else:
                raise ValueError(f"Unsupported dataset type: {self.dataset_type}")
            
            self.idx_to_user = {idx: uid for uid, idx in self.user_to_idx.items()}
            self.idx_to_item = {idx: iid for iid, idx in self.item_to_idx.items()}
            
            print(f"load mapping: {len(self.user_to_idx)} user, {len(self.item_to_idx)} item")
        else:
            raise FileNotFoundError(f"Graph id mappings not found at {mappings_path}")

    def train_user_item_embeddings(self, graph, batch_size=128, epochs=10, lr=0.01):
        num_users = graph['user'].num_nodes
        num_items = graph['item'].num_nodes
        total_nodes = num_users + num_items

        user_nodes_click = graph['user', 'click', 'item'].edge_index[0]
        item_nodes_click = graph['user', 'click', 'item'].edge_index[1] + num_users
        user_nodes_imp = graph['user', 'impression', 'item'].edge_index[0]
        item_nodes_imp = graph['user', 'impression', 'item'].edge_index[1] + num_users

        edge_index = torch.cat([
            torch.stack([user_nodes_click, item_nodes_click]),
            torch.stack([user_nodes_imp, item_nodes_imp])
        ], dim=1)

        model = Node2Vec(
            edge_index,
            embedding_dim=self.embedding_dim,
            walk_length=self.walk_length,
            context_size=self.context_size,
            walks_per_node=self.walks_per_node,
            p=self.p,
            q=self.q,
            num_negative_samples=self.num_negative_samples,
            sparse=True,
            num_nodes=total_nodes
        ).to(self.device)

        loader = model.loader(batch_size=batch_size, shuffle=True)
        optimizer = torch.optim.SparseAdam(model.parameters(), lr=lr)
        model.train()

        for epoch in tqdm(range(epochs)):
            total_loss = 0
            for pos_rw, neg_rw in loader:
                optimizer.zero_grad()
                loss = model.loss(pos_rw.to(self.device), neg_rw.to(self.device))
                loss.backward()
                optimizer.step()
                total_loss += loss.item()
            print(f'Epoch: {epoch:02d}, Loss: {total_loss/len(loader):.4f}')

        model.eval()
        with torch.no_grad():
            z = model()

        user_embeddings = z[:num_users]
        item_embeddings = z[num_users:total_nodes]

        self.models['user_item'] = model
        self.embeddings['user'] = user_embeddings
        self.embeddings['item'] = item_embeddings

        self.save_user_item_mappings(num_users, num_items)

        return {'user': user_embeddings, 'item': item_embeddings}
    
    def save_user_item_mappings(self, num_users, num_items):
        mappings_dir = os.path.join(self.data_dir, 'graph_embeddings/mappings')
        os.makedirs(mappings_dir, exist_ok=True)
        
        user_id_to_emb_idx = {}
        for node_idx in range(num_users):
            if node_idx in self.idx_to_user:
                user_id = self.idx_to_user[node_idx]
                emb_idx = node_idx
                
                user_id_to_emb_idx[user_id] = emb_idx
        
        item_id_to_emb_idx = {}
        for node_idx in range(num_items):
            if node_idx in self.idx_to_item:
                item_id = self.idx_to_item[node_idx]
                emb_idx = node_idx
                
                item_id_to_emb_idx[item_id] = emb_idx
        
        user_map_df = pd.DataFrame({'user_id': list(user_id_to_emb_idx.keys()),'embedding_idx': list(user_id_to_emb_idx.values())})
        user_map_df.to_csv(os.path.join(mappings_dir, 'user_id_to_embedding_idx.csv'), index=False)
        
        item_map_df = pd.DataFrame({'item_id': list(item_id_to_emb_idx.keys()),'embedding_idx': list(item_id_to_emb_idx.values())})
        item_map_df.to_csv(os.path.join(mappings_dir, 'item_id_to_embedding_idx.csv'), index=False)
        
        with open(os.path.join(mappings_dir, 'mapping_info.txt'), 'w') as f:
            f.write(f"User embeddings shape: {self.embeddings['user'].shape}\n")
            f.write(f"Item embeddings shape: {self.embeddings['item'].shape}\n")
            f.write(f"Number of mapped users: {len(user_id_to_emb_idx)}\n")
            f.write(f"Number of mapped items: {len(item_id_to_emb_idx)}\n")
        
        print(f"the mapping index of user-item graph is save at: {mappings_dir}")

    def train_homogeneous_embeddings(self, graph, node_type, edge_types, batch_size=128, epochs=10, lr=0.01):
        embeddings = {}

        for edge_type in tqdm(edge_types):
            edge_name = f"{node_type}_{edge_type}_{node_type}"
            edge_index = graph[node_type, edge_type, node_type].edge_index
            model = Node2Vec(
                edge_index,
                embedding_dim=self.embedding_dim,
                walk_length=self.walk_length,
                context_size=self.context_size,
                walks_per_node=self.walks_per_node,
                p=self.p,
                q=self.q,
                num_negative_samples=self.num_negative_samples,
                sparse=True
            ).to(self.device)

            loader = model.loader(batch_size=batch_size, shuffle=True)
            optimizer = torch.optim.SparseAdam(model.parameters(), lr=lr)
            model.train()

            for epoch in tqdm(range(epochs)):
                total_loss = 0
                for pos_rw, neg_rw in loader:
                    optimizer.zero_grad()
                    loss = model.loss(pos_rw.to(self.device), neg_rw.to(self.device))
                    loss.backward()
                    optimizer.step()
                    total_loss += loss.item()
                print(f'Epoch: {epoch:02d}, Loss: {total_loss/len(loader):.4f}')

            model.eval()
            with torch.no_grad():
                z = model()

            self.models[edge_name] = model
            self.embeddings[edge_name] = z
            embeddings[edge_type] = z

            self.save_homogeneous_mappings(node_type, edge_type, graph[node_type].num_nodes)

        return embeddings

    def save_homogeneous_mappings(self, node_type, edge_type, num_nodes):
        mappings_dir = os.path.join(self.data_dir, 'graph_embeddings/mappings')
        os.makedirs(mappings_dir, exist_ok=True)
        
        edge_name = f"{node_type}_{edge_type}_{node_type}"
        
        if node_type == 'user':
            # user-user graph
            id_to_emb_idx = {}
            emb_idx_to_id = {}
            
            for node_idx in range(num_nodes):
                if node_idx in self.idx_to_user:
                    user_id = self.idx_to_user[node_idx]
                    emb_idx = node_idx
                    
                    id_to_emb_idx[user_id] = emb_idx
                    emb_idx_to_id[emb_idx] = user_id
            
            map_df = pd.DataFrame({'user_id': list(id_to_emb_idx.keys()),'embedding_idx': list(id_to_emb_idx.values())})
            map_df.to_csv(os.path.join(mappings_dir, f'user_{edge_name}_id_mapping.csv'), index=False)
            
        elif node_type == 'item':
            id_to_emb_idx = {}
            emb_idx_to_id = {}
            
            for node_idx in range(num_nodes):
                if node_idx in self.idx_to_item:
                    item_id = self.idx_to_item[node_idx]
                    emb_idx = node_idx
                    
                    id_to_emb_idx[item_id] = emb_idx
                    emb_idx_to_id[emb_idx] = item_id
            
            map_df = pd.DataFrame({'item_id': list(id_to_emb_idx.keys()),'embedding_idx': list(id_to_emb_idx.values())})
            map_df.to_csv(os.path.join(mappings_dir, f'item_{edge_name}_id_mapping.csv'), index=False)
        
        print(f"the mapping index of {node_type} {edge_name} graph is save at: {mappings_dir}")

    def save_embeddings(self, save_dir):
        os.makedirs(save_dir, exist_ok=True)

        for name, embedding in self.embeddings.items():
            torch.save(embedding, os.path.join(save_dir, f'{name}_graph_embeddings.pt'))

if __name__ == "__main__":
    dataset_type = 'bookcrossing'
    
    if dataset_type == 'movielens':
        data_dir = '/data/datasets/processed_datasets/movielens'
        graphs_dir = os.path.join(data_dir, 'graph')
        save_dir = os.path.join(data_dir, 'graph_embeddings')
        # MovieLens specific parameters
        walk_length = 20
        walks_per_node = 10
        q = 2.0
    elif dataset_type == 'bookcrossing':
        data_dir = '/data/datasets/processed_datasets/bookcrossing'
        graphs_dir = os.path.join(data_dir, 'graph')
        save_dir = os.path.join(data_dir, 'graph_embeddings')
        # BookCrossing specific parameters
        walk_length = 30
        walks_per_node = 15
        q = 1.5
    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    os.makedirs(save_dir, exist_ok=True)
    
    # Load graphs
    user_item_graph = torch.load(os.path.join(graphs_dir, 'user_item_graph.pt'), weights_only=False)
    user_graph = torch.load(os.path.join(graphs_dir, 'user_graph.pt'), weights_only=False)
    item_graph = torch.load(os.path.join(graphs_dir, 'item_graph.pt'), weights_only=False)
    
    # Initialize embedder with dataset-specific parameters
    embedder = GraphEmbedder(
        data_dir=data_dir,
        dataset_type=dataset_type,
        embedding_dim=64,  # Keep consistent for both datasets
        walk_length=walk_length,
        context_size=10,   # Keep consistent for both datasets
        walks_per_node=walks_per_node,
        p=0.5,            # Keep consistent for both datasets
        q=q,
        device=device
    )
    
    # Train user-item embeddings
    print("Training user-item embeddings")
    user_item_embeddings = embedder.train_user_item_embeddings(user_item_graph, batch_size=128, epochs=10)
    
    # Train user-user embeddings
    print("Training user-user embeddings")
    user_embeddings = embedder.train_homogeneous_embeddings(user_graph, node_type='user', edge_types=['co_click', 'co_impression'], batch_size=128, epochs=10)
    
    # Train item-item embeddings
    print("Training item-item embeddings")
    item_embeddings = embedder.train_homogeneous_embeddings(item_graph, node_type='item', edge_types=['co_clicked', 'co_impressed'], batch_size=128, epochs=10)
    
    # Save embeddings
    print("Saving embeddings")
    embedder.save_embeddings(save_dir)