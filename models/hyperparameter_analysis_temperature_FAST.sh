#!/usr/bin/env bash

DATA_DIR="/data/datasets/processed_datasets/bookcrossing"
OUTPUT_DIR="/data/datasets/processed_datasets/bookcrossing/temperature_analysis_FAST"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BASE_OUTPUT_DIR="${OUTPUT_DIR}/temp_analysis_${TIMESTAMP}"
BATCH_SIZE=512  # Larger batch size for speed (balanced with memory)
NUM_EPOCHS=1
LEARNING_RATE=1e-3  # Slightly higher LR for faster convergence
DEVICE="cuda"
WANDB_PROJECT="GraphLLM4CTR_Hyperparameter_Temperature_Bookcrossing_contrastive_FAST"
DATASET_TYPE="bookcrossing"

# Create base output directory
mkdir -p "${BASE_OUTPUT_DIR}"

# Log file
LOG_FILE="${BASE_OUTPUT_DIR}/temperature_analysis_FAST.log"
touch "${LOG_FILE}"

echo "=========================================="
echo "ULTRA-FAST Contrastive Learning Training"
echo "Dataset: Full BookCrossing (795K samples)"
echo "Batch Size: ${BATCH_SIZE}"
echo "Optimizations: Ultra-fast batch builder"
echo "Expected time: ~1-2 hours (vs 14 hours)"
echo "=========================================="

# Temperature values to analyze
TEMPERATURES=(0.05)

# Run for each temperature
for TEMP in "${TEMPERATURES[@]}"; do
    echo "=========================================="
    echo "Analyzing Temperature: ${TEMP}"
    echo "Start time: $(date)"
    echo "=========================================="

    # Create directory for this temperature
    TEMP_DIR="${BASE_OUTPUT_DIR}/temp_${TEMP}"
    mkdir -p "${TEMP_DIR}"

    # Run ultra-fast contrastive learning
    python contrastive_learning_train_for_check.py \
        --data_dir "${DATA_DIR}" \
        --model_save_dir "${TEMP_DIR}" \
        --batch_size "${BATCH_SIZE}" \
        --num_epochs "${NUM_EPOCHS}" \
        --learning_rate "${LEARNING_RATE}" \
        --device "${DEVICE}" \
        --temperature "${TEMP}" \
        --wandb_project "${WANDB_PROJECT}" \
        --dataset_type "${DATASET_TYPE}" 2>&1 | tee -a "${LOG_FILE}"

    echo "=========================================="
    echo "Temperature ${TEMP} completed at: $(date)"
    echo "=========================================="
done

echo "=========================================="
echo "ULTRA-FAST training completed!"
echo "Total time: $(date)"
echo "Results saved to: ${BASE_OUTPUT_DIR}"
echo "=========================================="
