#!/usr/bin/env python3
"""
Run CTR prediction using BookCrossing contrastive learning results
"""

import os
import sys
from hyperparameter_analysis_embedding import run_embedding_size_analysis
import argparse

def main():
    """Run CTR prediction with BookCrossing contrastive learning results"""
    
    # Get the latest contrastive learning results
    base_dir = "/data/datasets/processed_datasets/bookcrossing"
    temp_analysis_dir = os.path.join(base_dir, "temperature_analysis_FAST")
    
    # Find the latest temperature analysis results
    if os.path.exists(temp_analysis_dir):
        analysis_dirs = [d for d in os.listdir(temp_analysis_dir) if d.startswith("temp_analysis_")]
        if analysis_dirs:
            latest_analysis = sorted(analysis_dirs)[-1]
            contrastive_results_dir = os.path.join(temp_analysis_dir, latest_analysis, "temp_0.2")
            
            if os.path.exists(contrastive_results_dir):
                print(f"Found contrastive learning results: {contrastive_results_dir}")
                
                # Set up paths for BookCrossing
                embedding_dir = os.path.join(contrastive_results_dir, "aligned_embeddings_epoch1")
                qformer_ckpt = os.path.join(contrastive_results_dir, "qformer_epoch1.pt")
                
                # Check if required files exist
                if not os.path.exists(embedding_dir):
                    print(f"❌ Embedding directory not found: {embedding_dir}")
                    return
                    
                if not os.path.exists(qformer_ckpt):
                    print(f"❌ Q-Former checkpoint not found: {qformer_ckpt}")
                    return
                
                # Create arguments for CTR prediction
                class Args:
                    def __init__(self):
                        # BookCrossing dataset paths
                        self.train_path = os.path.join(base_dir, "train.csv")
                        self.val_path = os.path.join(base_dir, "val.csv") 
                        self.test_path = os.path.join(base_dir, "test.csv")
                        
                        # Contrastive learning results
                        self.embedding_dir = embedding_dir
                        self.qformer_ckpt = qformer_ckpt
                        
                        # Use train.csv as item metadata (contains item_id and title)
                        self.item_meta_path = self.train_path
                        
                        # Training parameters
                        self.epochs = 15
                        self.batch_size = 128  # Reduced for memory
                        self.lr = 0.002
                        self.weight_decay = 1e-5
                        self.early_stop_patience = 5
                        self.warmup_steps = 500
                        self.max_grad_norm = 1.0
                        self.seed = 42
                        
                        # Output directory
                        self.log_dir = os.path.join(base_dir, "ctr_analysis")
                        self.device = "cuda"
                        self.use_wandb = True

                        self.dataset_type = 'bookcrossing'
                
                args = Args()
                
                # Verify all required files exist
                required_files = [args.train_path, args.val_path, args.test_path]
                for file_path in required_files:
                    if not os.path.exists(file_path):
                        print(f"❌ Required file not found: {file_path}")
                        return
                
                print("✅ All required files found!")
                print(f"📊 Starting CTR prediction with contrastive learning results...")
                print(f"   Embedding dir: {args.embedding_dir}")
                print(f"   Q-Former checkpoint: {args.qformer_ckpt}")
                print(f"   Output dir: {args.log_dir}")
                
                # Run the analysis
                try:
                    results = run_embedding_size_analysis(args)
                    print("🎉 CTR prediction completed successfully!")
                    print(f"📈 Results saved to: {args.log_dir}")
                    return results
                except Exception as e:
                    print(f"❌ Error during CTR prediction: {e}")
                    import traceback
                    traceback.print_exc()
                    return None
            else:
                print(f"❌ Contrastive results directory not found: {contrastive_results_dir}")
        else:
            print(f"❌ No temperature analysis directories found in: {temp_analysis_dir}")
    else:
        print(f"❌ Temperature analysis directory not found: {temp_analysis_dir}")
        print("Please run hyperparameter_analysis_temperature_FAST.sh first!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--contrastive_dir", type=str, help="Override contrastive learning results directory")
    args = parser.parse_args()
    
    if args.contrastive_dir:
        print(f"Using specified contrastive directory: {args.contrastive_dir}")
        # TODO: Implement custom directory logic
    
    main()
