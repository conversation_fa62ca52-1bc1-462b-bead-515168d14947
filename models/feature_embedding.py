import torch
import torch.nn as nn
import os
import pandas as pd
import ast

class FeatureEmbedder(nn.Module):
    def __init__(self, feature_dims, embedding_dims, device):
        super().__init__()
        self.device = device
        self.feature_dims = feature_dims
        self.embedding_dims = embedding_dims
        self.embedding_layers = nn.ModuleDict()

        for feature_name in feature_dims:
            self.embedding_layers[feature_name] = nn.Embedding(
                num_embeddings=feature_dims[feature_name],
                embedding_dim=embedding_dims[feature_name],
                padding_idx=0
            )

        self.to(device)

    def forward(self, processed_features):
        embeddings = []
        for feature_name, indices in processed_features.items():
            indices = torch.clamp(indices, min=0, max=self.feature_dims[feature_name]-1)
            indices = indices.to(self.device)

            if feature_name == 'genres':
                mask = (indices != 0).unsqueeze(-1)
                genre_embeddings = self.embedding_layers[feature_name](indices)
                summed = (genre_embeddings * mask).sum(dim=1)
                count = mask.sum(dim=1).clamp(min=1)
                genre_embedding = summed / count
                embeddings.append(genre_embedding)
            else:
                feature_embedding = self.embedding_layers[feature_name](indices)
                embeddings.append(feature_embedding)

        return torch.cat(embeddings, dim=-1)

    @classmethod
    def load_pretrained(cls, model_path, device):
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        model = cls(
            feature_dims=checkpoint['feature_dims'],
            embedding_dims=checkpoint['embedding_dims'],
            device=device
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        return model

    def save_pretrained(self, save_path):
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        torch.save({
            'model_state_dict': self.state_dict(),
            'feature_dims': self.feature_dims,
            'embedding_dims': self.embedding_dims
        }, save_path)

    def to_torchscript(self, save_path):
        os.makedirs(save_path, exist_ok=True)
        scripted = torch.jit.script(self)
        scripted.save(save_path)


class FeatureProcessor:
    def __init__(self, data_dir, dataset_type='movielens'):
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.feature_dims = None
        self.max_genres = None
        self._initialize_feature_dims()

    def _initialize_feature_dims(self):
        train_data = pd.read_csv(f"{self.data_dir}/train.csv")
        all_data = train_data

        if self.dataset_type == 'movielens':
            self._initialize_movielens_dims(all_data)
        elif self.dataset_type == 'bookcrossing':
            self._initialize_bookcrossing_dims(all_data)
        elif self.dataset_type == 'amazon':
            self._initialize_amazon_dims(all_data)
        else:
            raise ValueError(f"check dataset: {self.dataset_type} !!!")

    def _initialize_movielens_dims(self, all_data):
        # process user
        user_data = all_data[['user_id', 'gender', 'age', 'occupation', 'zip_code_index']].drop_duplicates()
        user_data = user_data.sort_values('user_id').reset_index(drop=True)

        # process item
        item_data = all_data[['movie_id', 'genres_index']].drop_duplicates()
        item_data = item_data.sort_values('movie_id').reset_index(drop=True)

        self.user_feature_dims = self._calculate_movielens_user_dims(user_data)
        self.item_feature_dims, self.max_genres = self._calculate_movielens_item_dims(item_data)

        self._save_id_mappings(user_data, item_data)

    def _initialize_bookcrossing_dims(self, all_data):
        # process user
        user_data = all_data[['user_id', 'age', 'city_index', 'state_index', 'country_index']].drop_duplicates()
        user_data = user_data.sort_values('user_id').reset_index(drop=True)

        # process item
        item_data = all_data[['item_id', 'author_index', 'publisher_index', 'year']].drop_duplicates()
        item_data = item_data.sort_values('item_id').reset_index(drop=True)

        # Load mappings from processed directory (consistent with build script)
        mapping_dir = os.path.join(self.data_dir, 'id_mappings')

        # Load user_id mapping
        user_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'user_id_mapping.csv'))
        user_id_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['index']))
        user_data['user_index'] = user_data['user_id'].map(user_id_to_idx)

        # Load item_id mapping
        item_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'item_id_mapping.csv'))
        item_id_to_idx = dict(zip(item_mapping_df['item_id'], item_mapping_df['index']))
        item_data['item_index'] = item_data['item_id'].map(item_id_to_idx)

        self.user_feature_dims = self._calculate_bookcrossing_user_dims(user_data)
        self.item_feature_dims = self._calculate_bookcrossing_item_dims(item_data)

        self._save_id_mappings(user_data, item_data)

    def _initialize_amazon_dims(self, all_data):
        # process user - Amazon users only have user_id
        user_data = all_data[['user_id']].drop_duplicates()
        user_data = user_data.sort_values('user_id').reset_index(drop=True)

        # process item - Amazon items have brand, categories, price_range
        item_data = all_data[['item_id', 'brand_index', 'category_indices', 'price_range_index']].drop_duplicates()
        item_data = item_data.sort_values('item_id').reset_index(drop=True)

        self.user_feature_dims = self._calculate_amazon_user_dims(user_data)
        self.item_feature_dims = self._calculate_amazon_item_dims(item_data)

        self._save_id_mappings(user_data, item_data)

    def _calculate_movielens_user_dims(self, user_data):
        dims = {
            'user_id': user_data['user_id'].max() + 1,
            'gender': user_data['gender'].max() + 1,
            'age': user_data['age'].max() + 1,
            'occupation': user_data['occupation'].max() + 1,
            'zip_code': user_data['zip_code_index'].max() + 1
        }
        return dims

    def _calculate_movielens_item_dims(self, item_data):
        max_genre_idx = 0
        max_genres = 0
        for genres in item_data['genres_index'].apply(ast.literal_eval):
            if genres:
                max_genres = max(max_genres, len(genres))
                if len(genres) > 0:
                    max_genre_idx = max(max_genre_idx, max(genres))

        dims = {
            'movie_id': item_data['movie_id'].max() + 1,
            'genres': max_genre_idx + 1
        }
        return dims, max_genres

    def _calculate_bookcrossing_user_dims(self, user_data):
        dims = {
            'user_id': user_data['user_index'].max() + 1,
            'age': user_data['age'].max() + 1,
            'city': user_data['city_index'].max() + 1,
            'state': user_data['state_index'].max() + 1,
            'country': user_data['country_index'].max() + 1
        }
        return dims

    def _calculate_bookcrossing_item_dims(self, item_data):
        dims = {
            'item_id': item_data['item_index'].max() + 1,
            'author': item_data['author_index'].max() + 1,
            'publisher': item_data['publisher_index'].max() + 1,
            'year': item_data['year'].max() + 1
        }
        return dims

    def _calculate_amazon_user_dims(self, user_data):
        dims = {
            'user_id': user_data['user_id'].max() + 1
        }
        return dims

    def _calculate_amazon_item_dims(self, item_data):
        # Calculate max category index from category_indices lists
        max_category_idx = 0
        for cat_indices in item_data['category_indices'].apply(ast.literal_eval):
            if cat_indices:
                max_category_idx = max(max_category_idx, max(cat_indices))

        dims = {
            'item_id': item_data['item_id'].max() + 1,
            'brand': item_data['brand_index'].max() + 1,
            'categories': max_category_idx + 1,
            'price_range': item_data['price_range_index'].max() + 1
        }
        return dims

    def _save_id_mappings(self, user_data, item_data):
        save_dir = os.path.join(self.data_dir, 'id_mappings')
        os.makedirs(save_dir, exist_ok=True)

        if self.dataset_type == 'movielens':
            user_mapping = {id_val: idx for idx, id_val in enumerate(user_data['user_id'])}
            df_user_mapping = pd.DataFrame(list(user_mapping.items()), columns=['user_id', 'embedding_idx'])
            df_user_mapping.to_csv(f"{save_dir}/user_id_to_embedding_index_map.csv", index=False)

            item_mapping = {id_val: idx for idx, id_val in enumerate(item_data['movie_id'])}
            df_item_mapping = pd.DataFrame(list(item_mapping.items()), columns=['movie_id', 'embedding_idx'])
            df_item_mapping.to_csv(f"{save_dir}/item_id_to_embedding_index_map.csv", index=False)
        elif self.dataset_type == 'bookcrossing':
            user_mapping = {id_val: idx for idx, id_val in enumerate(user_data['user_id'])}
            df_user_mapping = pd.DataFrame(list(user_mapping.items()), columns=['user_id', 'embedding_idx'])
            df_user_mapping.to_csv(f"{save_dir}/user_id_to_embedding_index_map.csv", index=False)

            item_mapping = {id_val: idx for idx, id_val in enumerate(item_data['item_id'])}
            df_item_mapping = pd.DataFrame(list(item_mapping.items()), columns=['item_id', 'embedding_idx'])
            df_item_mapping.to_csv(f"{save_dir}/item_id_to_embedding_index_map.csv", index=False)
        elif self.dataset_type == 'amazon':
            user_mapping = {id_val: idx for idx, id_val in enumerate(user_data['user_id'])}
            df_user_mapping = pd.DataFrame(list(user_mapping.items()), columns=['user_id', 'embedding_idx'])
            df_user_mapping.to_csv(f"{save_dir}/user_id_to_embedding_index_map.csv", index=False)

            item_mapping = {id_val: idx for idx, id_val in enumerate(item_data['item_id'])}
            df_item_mapping = pd.DataFrame(list(item_mapping.items()), columns=['item_id', 'embedding_idx'])
            df_item_mapping.to_csv(f"{save_dir}/item_id_to_embedding_index_map.csv", index=False)
        else:
            raise ValueError(f"check dataset: {self.dataset_type} !!!")

def process_features(data, type='user', max_genres=18, dataset_type='movielens'):
    """
    Process features for users or items.
    """
    if type == 'user':
        if dataset_type == 'movielens':
            return {
                'user_id': torch.tensor(data['user_id'].values, dtype=torch.long),
                'gender': torch.tensor(data['gender'].values, dtype=torch.long),
                'age': torch.tensor(data['age'].values, dtype=torch.long),
                'occupation': torch.tensor(data['occupation'].values, dtype=torch.long),
                'zip_code': torch.tensor(data['zip_code_index'].values, dtype=torch.long)
            }
        elif dataset_type == 'bookcrossing':
            return {
                'user_id': torch.tensor(data['user_index'].values, dtype=torch.long),  # Use user_index for consistency
                'age': torch.tensor(data['age'].values, dtype=torch.long),
                'city': torch.tensor(data['city_index'].values, dtype=torch.long),
                'state': torch.tensor(data['state_index'].values, dtype=torch.long),
                'country': torch.tensor(data['country_index'].values, dtype=torch.long)
            }
        elif dataset_type == 'amazon':
            return {
                'user_id': torch.tensor(data['user_id'].values, dtype=torch.long)
            }
        else:
            raise ValueError(f"check dataset: {dataset_type} !!!")
    else:  # item
        if dataset_type == 'movielens':
            return {
                'movie_id': torch.tensor(data['movie_id'].values, dtype=torch.long),
                'genres': torch.tensor(data['genres_index'].values, dtype=torch.long)
            }
        elif dataset_type == 'bookcrossing':
            return {
                'item_id': torch.tensor(data['item_index'].values, dtype=torch.long),  # Use item_index instead of item_id
                'author': torch.tensor(data['author_index'].values, dtype=torch.long),
                'publisher': torch.tensor(data['publisher_index'].values, dtype=torch.long),
                'year': torch.tensor(data['year'].values, dtype=torch.long)
            }
        elif dataset_type == 'amazon':
            # Handle category_indices which are stored as string representations of lists
            category_data = []
            for cat_str in data['category_indices'].values:
                try:
                    # Convert string representation of list to actual list
                    cat_list = ast.literal_eval(cat_str) if isinstance(cat_str, str) else cat_str
                    # Take first category index (or 0 if empty)
                    cat_idx = cat_list[0] if cat_list and len(cat_list) > 0 else 0
                    category_data.append(cat_idx)
                except:
                    category_data.append(0)  # Default to 0 if parsing fails

            return {
                'item_id': torch.tensor(data['item_id'].values, dtype=torch.long),
                'brand': torch.tensor(data['brand_index'].values, dtype=torch.long),
                'categories': torch.tensor(category_data, dtype=torch.long),
                'price_range': torch.tensor(data['price_range_index'].values, dtype=torch.long)
            }
        else:
            raise ValueError(f"check dataset: {dataset_type} !!!")

if __name__ == "__main__":
    dataset_type = 'amazon'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    if dataset_type == 'movielens':
        data_dir = '/data/datasets/processed_datasets/movielens'
        processor = FeatureProcessor(data_dir, dataset_type=dataset_type)
        embedding_dims = {
            'user_id': 16,
            'movie_id': 16,
            'gender': 16,
            'age': 16,
            'occupation': 16,
            'zip_code': 16,
            'genres': 16
        }

        user_embedder = FeatureEmbedder(processor.user_feature_dims, embedding_dims, device)
        item_embedder = FeatureEmbedder(processor.item_feature_dims, embedding_dims, device)

        user_embedder.save_pretrained(f"{data_dir}/feature_embeddings/user_embedder.pt")
        item_embedder.save_pretrained(f"{data_dir}/feature_embeddings/item_embedder.pt")
    elif dataset_type == 'bookcrossing':
        data_dir = '/data/datasets/processed_datasets/bookcrossing'
        processor = FeatureProcessor(data_dir, dataset_type=dataset_type)
        embedding_dims = {
            'user_id': 16,
            'item_id': 16,
            'age': 16,
            'city': 16,
            'state': 16,
            'country': 16,
            'author': 16,
            'publisher': 16,
            'year': 16
        }

        user_embedder = FeatureEmbedder(processor.user_feature_dims, embedding_dims, device)
        item_embedder = FeatureEmbedder(processor.item_feature_dims, embedding_dims, device)

        user_embedder.save_pretrained(f"{data_dir}/feature_embeddings/user_embedder.pt")
        item_embedder.save_pretrained(f"{data_dir}/feature_embeddings/item_embedder.pt")
    elif dataset_type == 'amazon':
        data_dir = '/data/datasets/processed_datasets/amazon'
        processor = FeatureProcessor(data_dir, dataset_type=dataset_type)
        embedding_dims = {
            'user_id': 16,
            'item_id': 16,
            'brand': 16,
            'categories': 16,
            'price_range': 16
        }

        user_embedder = FeatureEmbedder(processor.user_feature_dims, embedding_dims, device)
        item_embedder = FeatureEmbedder(processor.item_feature_dims, embedding_dims, device)

        user_embedder.save_pretrained(f"{data_dir}/feature_embeddings/user_embedder.pt")
        item_embedder.save_pretrained(f"{data_dir}/feature_embeddings/item_embedder.pt")
    else:
        raise ValueError(f"check dataset: {dataset_type} !!!")

