import os
import torch
from torch_geometric.utils import subgraph
from tqdm import tqdm

# 加载 edge_index
user_edge_type1 = torch.load("/root/code/GraphLLM4CTR/user_edge_type1_edge_index.pt")
user_edge_type2 = torch.load("/root/code/GraphLLM4CTR/user_edge_type2_edge_index.pt")
item_edge_type1 = torch.load("/root/code/GraphLLM4CTR/item_edge_type1_edge_index.pt")
item_edge_type2 = torch.load("/root/code/GraphLLM4CTR/item_edge_type2_edge_index.pt")

user_batches = torch.load("/data/datasets/processed_datasets/movielens/user_id_batches.pt")
item_batches = torch.load("/data/datasets/processed_datasets/movielens/item_id_batches.pt")

all_user_ids = torch.cat(user_batches)
all_item_ids = torch.cat(item_batches)

num_users = int(all_user_ids.max().item()) + 1
num_items = int(all_item_ids.max().item()) + 1

output_dir = "/data/datasets/processed_datasets/movielens/subgraph_cache"
os.makedirs(output_dir, exist_ok=True)

def save_subgraph(edge_index, node_ids, total_nodes, save_path):
    node_ids_unique = node_ids.unique().to(edge_index.device)
    edge_index = edge_index.to(node_ids_unique.device)
    sub_edge_index, _ = subgraph(node_ids_unique, edge_index, relabel_nodes=True, num_nodes=total_nodes)
    torch.save(sub_edge_index.cpu(), save_path)


print("Start caching subgraphs...")
for batch_idx in tqdm(range(len(user_batches))):
    u_ids = user_batches[batch_idx]
    i_ids = item_batches[batch_idx]

    save_subgraph(user_edge_type1, u_ids, num_users, f"{output_dir}/user_edge_type1_batch_{batch_idx}.pt")
    save_subgraph(user_edge_type2, u_ids, num_users, f"{output_dir}/user_edge_type2_batch_{batch_idx}.pt")
    save_subgraph(item_edge_type1, i_ids, num_items, f"{output_dir}/item_edge_type1_batch_{batch_idx}.pt")
    save_subgraph(item_edge_type2, i_ids, num_items, f"{output_dir}/item_edge_type2_batch_{batch_idx}.pt")

print("Done")
