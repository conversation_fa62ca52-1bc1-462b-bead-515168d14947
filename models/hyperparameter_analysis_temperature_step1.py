import os
import torch
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import subprocess
from tqdm import tqdm
import logging
import wandb

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_contrastive_learning(
    data_dir,
    output_dir,
    temperature,
    batch_size=128,
    num_epochs=1,
    learning_rate=5e-4,
    device="cuda",
    wandb_project="GraphLLM4CTR_Hyperparameter_Temperature"
):
    """
    Run contrastive learning with a specific temperature value
    """
    # Create output directory for this temperature
    temp_dir = os.path.join(output_dir, f"temp_{temperature}")
    os.makedirs(temp_dir, exist_ok=True)

    # Create command to run contrastive learning
    cmd = [
        "python", "GraphLLM4CTR/models/contrastive_learning_train_for_check.py",
        "--data_dir", data_dir,
        "--model_save_dir", temp_dir,
        "--batch_size", str(batch_size),
        "--num_epochs", str(num_epochs),
        "--learning_rate", str(learning_rate),
        "--device", device,
        "--wandb_project", wandb_project,
        "--temperature", str(temperature)  # Add temperature parameter
    ]

    # Run the command
    logger.info(f"Running contrastive learning with temperature={temperature}")
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)

    # Stream output
    for line in iter(process.stdout.readline, ""):
        logger.info(line.strip())

    # Wait for process to complete
    process.wait()

    # Check if process completed successfully
    if process.returncode != 0:
        logger.error(f"Contrastive learning failed with temperature={temperature}")
        for line in process.stderr:
            logger.error(line.strip())
        return None

    # Return the path to the output directory
    return temp_dir


def run_temperature_analysis(args):
    """
    Run analysis of different temperature values
    """
    # Create main output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_output_dir = os.path.join(args.output_dir, f"temp_analysis_{timestamp}")
    os.makedirs(base_output_dir, exist_ok=True)

    # Set up file handler for logging
    log_file = os.path.join(base_output_dir, 'temperature_analysis.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)

    # Temperature values to analyze
    temperatures = [0.07, 0.05, 0.1, 0.2, 0.5, 0.7]

    # Store results
    all_results = []

    # Run analysis for each temperature
    for temp in temperatures:
        logger.info(f"\n{'='*50}")
        logger.info(f"Analyzing Temperature: {temp}")
        logger.info(f"{'='*50}\n")

        # Step 1: Run contrastive learning with this temperature
        contrastive_dir = run_contrastive_learning(
            data_dir=args.data_dir,
            output_dir=base_output_dir,
            temperature=temp,
            batch_size=args.batch_size,
            num_epochs=args.contrastive_epochs,
            learning_rate=args.contrastive_lr,
            device=args.device,
            wandb_project=f"{args.wandb_project}_contrastive"
        )

        if contrastive_dir is None:
            logger.error(f"Skipping temperature {temp} due to contrastive learning failure")
            continue

    print('all the temperature are finish!')


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    # Data paths
    parser.add_argument("--data_dir", type=str, default="/data/datasets/processed_datasets/movielens")
    # Output directory
    parser.add_argument("--output_dir", type=str, default="/data/datasets/processed_datasets/movielens/temperature_analysis")
    # Contrastive learning parameters
    parser.add_argument("--contrastive_epochs", type=int, default=1)
    parser.add_argument("--batch_size", type=int, default=128)
    parser.add_argument("--contrastive_lr", type=float, default=5e-4)
    # General parameters
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--wandb_project", type=str, default="GraphLLM4CTR_Hyperparameter_Temperature")

    args = parser.parse_args()

    # Run temperature analysis
    results = run_temperature_analysis(args)
