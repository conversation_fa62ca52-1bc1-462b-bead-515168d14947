import torch
import torch.nn as nn
import torch.nn.functional as F


class EnhancedMLP(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, dropout=0.1):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.LayerNorm(hidden_dim),  # Added layer normalization
            nn.Linear(hidden_dim, output_dim)
        )

    def forward(self, x):
        return self.mlp(x)


class EnhancedGatingNetwork(nn.Module):
    def __init__(self, input_dim, num_experts, hidden_dim=64):
        super().__init__()
        self.gate = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, num_experts)
        )
        
    def forward(self, x):
        # x: [B, D]
        return F.softmax(self.gate(x), dim=-1)  # [B, N]


class FocusedHybridExpertAdaptor(nn.Module):
    def __init__(
        self,
        input_dim,
        expert_output_dim,
        num_shared_experts=3,  # Increased from 2 to 3
        num_user_experts=3,    # Increased from 2 to 3
        num_item_experts=3,    # Increased from 2 to 3
        hidden_dim=128,
        dropout=0.1
    ):
        super().__init__()
        self.total_user_experts = num_shared_experts + num_user_experts
        self.total_item_experts = num_shared_experts + num_item_experts

        # Shared Experts
        self.shared_experts = nn.ModuleList([
            EnhancedMLP(input_dim, hidden_dim, expert_output_dim, dropout)
            for _ in range(num_shared_experts)
        ])

        # User-specific Experts
        self.user_experts = nn.ModuleList([
            EnhancedMLP(input_dim, hidden_dim, expert_output_dim, dropout)
            for _ in range(num_user_experts)
        ])

        # Item-specific Experts
        self.item_experts = nn.ModuleList([
            EnhancedMLP(input_dim, hidden_dim, expert_output_dim, dropout)
            for _ in range(num_item_experts)
        ])

        # Enhanced Gating networks
        self.gate_user = EnhancedGatingNetwork(input_dim, self.total_user_experts)
        self.gate_item = EnhancedGatingNetwork(input_dim, self.total_item_experts)
        
        # Layer normalization for final representations
        self.user_norm = nn.LayerNorm(expert_output_dim)
        self.item_norm = nn.LayerNorm(expert_output_dim)

        # CTR prediction MLP with layer normalization
        self.ctr_mlp = nn.Sequential(
            nn.Linear(expert_output_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.LayerNorm(hidden_dim),
            nn.Linear(hidden_dim, 1)  # Logit
        )

    def forward(self, user_input, item_input):
        """
        user_input: [B, D]
        item_input: [B, D]
        Returns:
            ctr_prob: [B, 1]  (sigmoid)
            user_repr: [B, expert_output_dim]
            item_repr: [B, expert_output_dim]
        """
        # --- User Experts ---
        all_user_experts = list(self.shared_experts) + list(self.user_experts)
        user_expert_outputs = torch.stack(
            [net(user_input) for net in all_user_experts], dim=1
        )  # [B, N, D]
        
        # Get expert weights using enhanced gating network
        user_gate_weights = self.gate_user(user_input).unsqueeze(-1)  # [B, N, 1]
        
        # Weighted sum of expert outputs
        user_repr = torch.sum(user_expert_outputs * user_gate_weights, dim=1)  # [B, D]
        user_repr = self.user_norm(user_repr)  # Apply layer normalization

        # --- Item Experts ---
        all_item_experts = list(self.shared_experts) + list(self.item_experts)
        item_expert_outputs = torch.stack(
            [net(item_input) for net in all_item_experts], dim=1
        )  # [B, N, D]
        
        # Get expert weights using enhanced gating network
        item_gate_weights = self.gate_item(item_input).unsqueeze(-1)  # [B, N, 1]
        
        # Weighted sum of expert outputs
        item_repr = torch.sum(item_expert_outputs * item_gate_weights, dim=1)  # [B, D]
        item_repr = self.item_norm(item_repr)  # Apply layer normalization

        # --- CTR Prediction ---
        ctr_input = torch.cat([user_repr, item_repr], dim=-1)
        ctr_logit = self.ctr_mlp(ctr_input)
        ctr_prob = torch.sigmoid(ctr_logit)

        return ctr_prob, user_repr, item_repr
