import torch
import torch.nn as nn
from transformers import AutoModel

class LLMMessageUpdater(nn.Module):
    def __init__(self,
                 llm_model_name,
                 gnn_num_layers,
                 llm_num_layers,
                 layer_idx,
                 input_dim,
                 hidden_dim,
                 adapter_dim=128,
                 freeze_llm=True,
                 device="cpu"):
        super().__init__()
        self.device = device
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # Project input to hidden dimension
        self.input_proj = nn.Linear(input_dim, hidden_dim)

        # RoBERTa's hidden dimension is 768
        self.roberta_dim = 768

        # Create a projection layer to match RoBERTa's dimension if needed
        if hidden_dim != self.roberta_dim:
            self.dim_proj = nn.Linear(hidden_dim, self.roberta_dim)
        else:
            self.dim_proj = nn.Identity()

        if llm_model_name == "roberta-base":
            full_model = AutoModel.from_pretrained('/root/code/roberta-base')
        else:
            raise ValueError('this LLM is not supported now')
        if freeze_llm:
            for p in full_model.parameters():
                p.requires_grad = False
        self.embeddings = full_model.embeddings

        start = int(layer_idx * llm_num_layers / gnn_num_layers)
        end = int((layer_idx + 1) * llm_num_layers / gnn_num_layers)
        self.encoder_layers = nn.ModuleList(full_model.encoder.layer[start:end])
        self.layer_norm = full_model.layernorm if hasattr(full_model, "layernorm") else nn.Identity()

        # Adapter to project from RoBERTa's dimension back to hidden dimension
        self.adapter = nn.Sequential(
            nn.Linear(self.roberta_dim, adapter_dim),
            nn.ReLU(),
            nn.Linear(adapter_dim, hidden_dim)
        )

    def forward(self, x):
        # print("\n=== Debug: LLMMessageUpdater.forward ===")
        # print(f"Input x shape: {x.shape}, dtype: {x.dtype}, device: {x.device}")

        # Project input to match RoBERTa's hidden dimension (768)
        projected = self.input_proj(x)
        # print(f"After projection shape: {projected.shape}")

        # Add a sequence dimension (batch_size, seq_len=1, hidden_dim)
        hidden = projected.unsqueeze(1)
        # print(f"After unsqueeze shape: {hidden.shape}")

        # Project to RoBERTa's dimension using the pre-defined projection layer
        hidden = self.dim_proj(hidden)
        # print(f"After dimension projection shape: {hidden.shape}")

        # Create attention mask with the correct dtype (float) and shape
        # For RoBERTa, the attention mask should be [batch_size, 1, 1, seq_len]
        # This creates a causal mask that allows each token to attend to all previous tokens
        batch_size, seq_len = hidden.shape[:2]

        # Create a simple mask that allows full attention (all 1s)
        # Shape: [batch_size, 1, seq_len]
        attention_mask = torch.ones((batch_size, 1, seq_len), dtype=torch.float32).to(self.device)

        # Convert to the format expected by the model
        # This creates a 4D mask [batch_size, 1, 1, seq_len]
        extended_attention_mask = attention_mask.unsqueeze(1)
        # print(f"Attention mask shape: {extended_attention_mask.shape}, dtype: {extended_attention_mask.dtype}")

        embedding_output = self.embeddings(inputs_embeds=hidden)
        # print(f"After embeddings shape: {embedding_output.shape}")

        encoder_output = embedding_output
        for i, layer in enumerate(self.encoder_layers):
            # Pass the extended attention mask to the layer
            encoder_output = layer(encoder_output, attention_mask=extended_attention_mask)[0]
            # print(f"After encoder layer {i} shape: {encoder_output.shape}")

        output = self.layer_norm(encoder_output)
        # print(f"After layer_norm shape: {output.shape}")

        adapted = self.adapter(output)
        # print(f"After adapter shape: {adapted.shape}")

        # Return the squeezed output (remove sequence dimension)
        return adapted.squeeze(1)
