2025-05-28 09:56:31,335 - __main__ - INFO - Loading dataset and dataloader
2025-05-28 09:56:34,124 - __main__ - INFO - Initializing batch builder

=== Initializing OptimizedBatchBuilder ===
Data directory: /data/datasets/processed_datasets/amazon
Dataset type: amazon
Batch size: 512
Device: cuda
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:158: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_user_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:160: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_item_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:162: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:164: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:166: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:168: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:173: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:174: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.click_matrix = torch.load(f"{graph_matrix_dir}/click_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:63: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.impression_matrix = torch.load(f"{graph_matrix_dir}/impression_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:64: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1 = torch.load(f"{graph_matrix_dir}/user_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:65: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2 = torch.load(f"{graph_matrix_dir}/user_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:66: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1 = torch.load(f"{graph_matrix_dir}/item_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:67: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2 = torch.load(f"{graph_matrix_dir}/item_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:44: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:45: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type2.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:46: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:47: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type2.pt")
2025-05-28 09:56:40,759 - __main__ - INFO - Initializing contrastive learning model
contrastive_learning_train_for_check.py:217: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
2025-05-28 09:56:41,162 - __main__ - INFO - Starting training
  0%|          | 0/1 [00:00<?, ?it/s]              contrastive_learning_train_for_check.py:256: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():        | 0/2124 [00:00<?, ?it/s]
Lambda values: λ1=1.0000, λ2=1.0000, λ3=1.0000, λ4=1.0000, λ5=1.0000
[Debug] Raw Grad Norm: inf
[Debug] Grad Norm: nan | Update Norm: nan
[Debug] Update Ratio: nan
/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/optim/lr_scheduler.py:216: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn(
2025-05-28 09:56:46,001 - __main__ - INFO - Batch 0/2124, GPU Memory: 1.80GB
2025-05-28 09:56:46,001 - __main__ - INFO -   user_edge_type1: nnz=0
2025-05-28 09:56:46,002 - __main__ - INFO -   user_edge_type2: nnz=0
2025-05-28 09:56:46,002 - __main__ - INFO -   item_edge_type1: nnz=0
2025-05-28 09:56:46,002 - __main__ - INFO -   item_edge_type2: nnz=2

Epoch 1/1:   0%|          | 8/2124 [00:29<2:04:48,  3.54s/it]
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.1080
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: inf
[Debug] Grad Norm: nan | Update Norm: nan
[Debug] Update Ratio: nan
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.4294
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.6187
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.8469
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.0923
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3854
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.8000, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.5056
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.8000, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3548
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.8000, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4220
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 09:57:20,363 - __main__ - INFO -   user_edge_type2: nnz=0
2025-05-28 09:57:20,363 - __main__ - INFO -   item_edge_type1: nnz=0
2025-05-28 09:57:20,363 - __main__ - INFO -   item_edge_type2: nnz=0

Epoch 1/1:   1%|          | 18/2124 [01:02<1:57:00,  3.33s/it]
Lambda values: λ1=0.7999, λ2=0.8000, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.1575
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.8000, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.4844
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7999, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3467
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7999, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.4250
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7999, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3568
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7997, λ2=0.7999, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3195
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7997, λ2=0.7999, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.9863
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7999, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3665
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7999, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3316
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7999, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3186
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 09:57:53,631 - __main__ - INFO -   user_edge_type2: nnz=0
2025-05-28 09:57:53,631 - __main__ - INFO -   item_edge_type1: nnz=1
2025-05-28 09:57:53,631 - __main__ - INFO -   item_edge_type2: nnz=2
Epoch 1/1:   1%|          | 24/2124 [01:25<2:04:17,  3.55s/it]Exception ignored in: <generator object tqdm.__iter__ at 0x7fb23892fac0>
Traceback (most recent call last):4 [01:22<1:55:43,  3.31s/it]
Lambda values: λ1=0.7995, λ2=0.7999, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5649
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7995, λ2=0.7999, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5641
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7994, λ2=0.7999, λ3=0.7994, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.9453
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1196, in __iter__
    self.close()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1302, in close
    self.display(pos=0)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1495, in display
    self.sp(self.__str__() if msg is None else msg)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 459, in print_status
    fp_write('\r' + s + (' ' * max(last_len[0] - len_s, 0)))
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 453, in fp_write
    fp_flush()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/utils.py", line 196, in inner
    return func(*args, **kwargs)
BrokenPipeError: [Errno 32] Broken pipe
Exception ignored in sys.unraisablehook: <built-in function unraisablehook>
BrokenPipeError: [Errno 32] Broken pipe
  0%|          | 0/1 [01:25<?, ?it/s]Exception ignored in: <generator object tqdm.__iter__ at 0x7fb1e22296d0>
Traceback (most recent call last):
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1196, in __iter__
    self.close()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1302, in close
    self.display(pos=0)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 1495, in display
    self.sp(self.__str__() if msg is None else msg)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 459, in print_status
    fp_write('\r' + s + (' ' * max(last_len[0] - len_s, 0)))
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/std.py", line 453, in fp_write
    fp_flush()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/tqdm/utils.py", line 196, in inner
    return func(*args, **kwargs)
BrokenPipeError: [Errno 32] Broken pipe
Exception ignored in sys.unraisablehook: <built-in function unraisablehook>
BrokenPipeError: [Errno 32] Broken pipe
Traceback (most recent call last):
  File "contrastive_learning_train_for_check.py", line 479, in <module>
    train_contrastive(
  File "contrastive_learning_train_for_check.py", line 251, in train_contrastive
    batch = batch_builder.build_batch(user_ids, item_ids, batch_data['label'])
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 459, in build_batch
    "item_edge_type2": edge_index_to_sparse(extract_subgraph(self.item_edge_type2_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 388, in extract_subgraph
    sample_indices = torch.randperm(edge_index.shape[1])[:max_edges]
KeyboardInterrupt
Traceback (most recent call last):
  File "contrastive_learning_train_for_check.py", line 479, in <module>
    train_contrastive(
  File "contrastive_learning_train_for_check.py", line 251, in train_contrastive
    batch = batch_builder.build_batch(user_ids, item_ids, batch_data['label'])
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 459, in build_batch
    "item_edge_type2": edge_index_to_sparse(extract_subgraph(self.item_edge_type2_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 388, in extract_subgraph
    sample_indices = torch.randperm(edge_index.shape[1])[:max_edges]
KeyboardInterrupt
