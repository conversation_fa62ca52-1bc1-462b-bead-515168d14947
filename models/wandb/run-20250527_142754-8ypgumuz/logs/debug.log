2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_setup.py:_flush():68] Configure stats pid to 162979
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250527_142754-8ypgumuz/logs/debug.log
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250527_142754-8ypgumuz/logs/debug-internal.log
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:init():852] calling init triggers
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:init():893] starting backend
2025-05-27 14:27:54,944 INFO    MainThread:162979 [wandb_init.py:init():897] sending inform_init request
2025-05-27 14:27:54,966 INFO    MainThread:162979 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-27 14:27:54,966 INFO    MainThread:162979 [wandb_init.py:init():907] backend started and connected
2025-05-27 14:27:54,969 INFO    MainThread:162979 [wandb_init.py:init():1002] updated telemetry
2025-05-27 14:27:55,013 INFO    MainThread:162979 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-27 14:27:56,058 INFO    MainThread:162979 [wandb_init.py:init():1101] starting run threads in backend
2025-05-27 14:27:56,183 INFO    MainThread:162979 [wandb_run.py:_console_start():2566] atexit reg
2025-05-27 14:27:56,183 INFO    MainThread:162979 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-27 14:27:56,184 INFO    MainThread:162979 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-27 14:27:56,184 INFO    MainThread:162979 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-27 14:27:56,186 INFO    MainThread:162979 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-27 14:27:56,186 INFO    MainThread:162979 [wandb_run.py:_config_callback():1429] config_cb None None {'embedding_size': 512, 'learning_rate': 0.002, 'epochs': 3, 'batch_size': 128, 'weight_decay': 1e-05, 'early_stop_patience': 5, 'warmup_steps': 500, 'max_grad_norm': 1.0, 'seed': 42}
2025-05-27 15:44:44,908 INFO    MainThread:162979 [wandb_run.py:_finish():2314] finishing run 9petrestaurant-huazhong-university-of-science-and-technology/GraphLLM4CTR_Hyperparameter_Embedding_Bookcrossing/8ypgumuz
2025-05-27 15:44:44,908 INFO    MainThread:162979 [wandb_run.py:_atexit_cleanup():2531] got exitcode: 0
2025-05-27 15:44:44,908 INFO    MainThread:162979 [wandb_run.py:_restore():2513] restore
2025-05-27 15:44:44,908 INFO    MainThread:162979 [wandb_run.py:_restore():2519] restore done
2025-05-27 15:44:47,039 INFO    MainThread:162979 [wandb_run.py:_footer_history_summary_info():4160] rendering history
2025-05-27 15:44:47,039 INFO    MainThread:162979 [wandb_run.py:_footer_history_summary_info():4192] rendering summary
2025-05-27 15:44:47,040 INFO    MainThread:162979 [wandb_run.py:_footer_sync_info():4121] logging synced files
