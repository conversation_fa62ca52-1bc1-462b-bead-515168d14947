{"time":"2025-05-28T06:19:03.656791127Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250528_061903-sozqd8zk/logs/debug-core.log"}
{"time":"2025-05-28T06:19:04.39045123Z","level":"INFO","msg":"created new stream","id":"sozqd8zk"}
{"time":"2025-05-28T06:19:04.390487287Z","level":"INFO","msg":"stream: started","id":"sozqd8zk"}
{"time":"2025-05-28T06:19:04.390512314Z","level":"INFO","msg":"writer: Do: started","stream_id":"sozqd8zk"}
{"time":"2025-05-28T06:19:04.390519442Z","level":"INFO","msg":"sender: started","stream_id":"sozqd8zk"}
{"time":"2025-05-28T06:19:04.390566407Z","level":"INFO","msg":"handler: started","stream_id":"sozqd8zk"}
{"time":"2025-05-28T06:19:04.759060279Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-28T07:36:01.29407526Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-28T07:36:01.294124248Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-28T07:36:02.29573242Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.397748473},{"desc":"uploading wandb-summary.json","runtime_seconds":0.397737915},{"desc":"uploading config.yaml","runtime_seconds":0.222616244}],"total_operations":3}}
{"time":"2025-05-28T07:36:02.98482557Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-28T07:36:03.80940536Z","level":"INFO","msg":"stream: closing","id":"sozqd8zk"}
{"time":"2025-05-28T07:36:03.809423759Z","level":"INFO","msg":"handler: closed","stream_id":"sozqd8zk"}
{"time":"2025-05-28T07:36:03.809438115Z","level":"INFO","msg":"sender: closed","stream_id":"sozqd8zk"}
{"time":"2025-05-28T07:36:03.809431462Z","level":"INFO","msg":"writer: Close: closed","stream_id":"sozqd8zk"}
{"time":"2025-05-28T07:36:03.809505414Z","level":"INFO","msg":"stream: closed","id":"sozqd8zk"}
