2025-05-28 09:48:52,254 - __main__ - INFO - Loading dataset and dataloader
2025-05-28 09:48:55,070 - __main__ - INFO - Initializing batch builder

=== Initializing OptimizedBatchBuilder ===
Data directory: /data/datasets/processed_datasets/amazon
Dataset type: amazon
Batch size: 512
Device: cuda
Traceback (most recent call last):
  File "contrastive_learning_train_for_check.py", line 479, in <module>
    train_contrastive(
  File "contrastive_learning_train_for_check.py", line 189, in train_contrastive
    batch_builder = OptimizedBatchBuilder(data_dir, batch_size, device, dataset_type=dataset_type)
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 23, in __init__
    self.load_embeddings()
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 137, in load_embeddings
    item_features = process_features(item_data, type='item', max_genres=max_genres, dataset_type=self.dataset_type)
  File "/root/code/GraphLLM4CTR/models/feature_embedding.py", line 284, in process_features
    'categories': torch.tensor(data['category_indices'].values, dtype=torch.long),
TypeError: can't convert np.ndarray of type numpy.object_. The only supported types are: float64, float32, float16, complex64, complex128, int64, int32, int16, int8, uint64, uint32, uint16, uint8, and bool.
Traceback (most recent call last):
  File "contrastive_learning_train_for_check.py", line 479, in <module>
    train_contrastive(
  File "contrastive_learning_train_for_check.py", line 189, in train_contrastive
    batch_builder = OptimizedBatchBuilder(data_dir, batch_size, device, dataset_type=dataset_type)
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 23, in __init__
    self.load_embeddings()
  File "/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py", line 137, in load_embeddings
    item_features = process_features(item_data, type='item', max_genres=max_genres, dataset_type=self.dataset_type)
  File "/root/code/GraphLLM4CTR/models/feature_embedding.py", line 284, in process_features
    'categories': torch.tensor(data['category_indices'].values, dtype=torch.long),
TypeError: can't convert np.ndarray of type numpy.object_. The only supported types are: float64, float32, float16, complex64, complex128, int64, int32, int16, int8, uint64, uint32, uint16, uint8, and bool.
