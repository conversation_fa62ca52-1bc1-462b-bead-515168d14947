{"time":"2025-05-27T11:42:31.077524541Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250527_114231-gc60ne1w/logs/debug-core.log"}
{"time":"2025-05-27T11:42:31.757021127Z","level":"INFO","msg":"created new stream","id":"gc60ne1w"}
{"time":"2025-05-27T11:42:31.757058605Z","level":"INFO","msg":"stream: started","id":"gc60ne1w"}
{"time":"2025-05-27T11:42:31.757085057Z","level":"INFO","msg":"handler: started","stream_id":"gc60ne1w"}
{"time":"2025-05-27T11:42:31.757085085Z","level":"INFO","msg":"writer: Do: started","stream_id":"gc60ne1w"}
{"time":"2025-05-27T11:42:31.757109246Z","level":"INFO","msg":"sender: started","stream_id":"gc60ne1w"}
{"time":"2025-05-27T11:42:32.079088588Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-27T12:59:08.493103005Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-27T12:59:08.493147378Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-27T12:59:09.494376425Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading wandb-summary.json","runtime_seconds":0.460098991,"progress":"606B/606B"},{"desc":"uploading output.log","runtime_seconds":0.460091705,"progress":"14.4KB/14.4KB"},{"desc":"uploading config.yaml","runtime_seconds":0.24632523,"progress":"948B/948B"}],"total_operations":3}}
{"time":"2025-05-27T12:59:09.979278936Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-27T12:59:10.614953423Z","level":"INFO","msg":"stream: closing","id":"gc60ne1w"}
{"time":"2025-05-27T12:59:10.614967313Z","level":"INFO","msg":"handler: closed","stream_id":"gc60ne1w"}
{"time":"2025-05-27T12:59:10.614973514Z","level":"INFO","msg":"writer: Close: closed","stream_id":"gc60ne1w"}
{"time":"2025-05-27T12:59:10.614982903Z","level":"INFO","msg":"sender: closed","stream_id":"gc60ne1w"}
{"time":"2025-05-27T12:59:10.615024565Z","level":"INFO","msg":"stream: closed","id":"gc60ne1w"}
