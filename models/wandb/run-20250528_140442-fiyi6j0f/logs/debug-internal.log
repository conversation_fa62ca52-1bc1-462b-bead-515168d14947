{"time":"2025-05-28T14:04:42.138024541Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250528_140442-fiyi6j0f/logs/debug-core.log"}
{"time":"2025-05-28T14:04:42.814731599Z","level":"INFO","msg":"created new stream","id":"fiyi6j0f"}
{"time":"2025-05-28T14:04:42.814760364Z","level":"INFO","msg":"stream: started","id":"fiyi6j0f"}
{"time":"2025-05-28T14:04:42.814783652Z","level":"INFO","msg":"writer: Do: started","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:04:42.814796528Z","level":"INFO","msg":"sender: started","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:04:42.814813454Z","level":"INFO","msg":"handler: started","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:04:43.129367341Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-28T14:38:31.086908918Z","level":"INFO","msg":"Stopping system monitor"}
{"time":"2025-05-28T14:38:31.086971158Z","level":"INFO","msg":"Stopped system monitor"}
{"time":"2025-05-28T14:38:32.088316552Z","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.47431546,"progress":"64.0KB/615.0KB"},{"desc":"uploading wandb-summary.json","runtime_seconds":0.474307496,"progress":"1.0KB/1.0KB"},{"desc":"uploading config.yaml","runtime_seconds":0.25788671,"progress":"849B/849B"}],"total_operations":3}}
{"time":"2025-05-28T14:38:33.3202226Z","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-05-28T14:38:33.991843354Z","level":"INFO","msg":"stream: closing","id":"fiyi6j0f"}
{"time":"2025-05-28T14:38:33.991857811Z","level":"INFO","msg":"handler: closed","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:38:33.991863857Z","level":"INFO","msg":"writer: Close: closed","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:38:33.991879744Z","level":"INFO","msg":"sender: closed","stream_id":"fiyi6j0f"}
{"time":"2025-05-28T14:38:33.991911884Z","level":"INFO","msg":"stream: closed","id":"fiyi6j0f"}
