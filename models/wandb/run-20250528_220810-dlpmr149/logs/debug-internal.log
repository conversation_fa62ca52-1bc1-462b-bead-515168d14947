{"time":"2025-05-28T22:08:10.948845054Z","level":"INFO","msg":"stream: starting","core version":"0.19.10","symlink path":"/root/code/GraphLLM4CTR/models/wandb/run-20250528_220810-dlpmr149/logs/debug-core.log"}
{"time":"2025-05-28T22:08:11.62513517Z","level":"INFO","msg":"created new stream","id":"dlpmr149"}
{"time":"2025-05-28T22:08:11.625158832Z","level":"INFO","msg":"stream: started","id":"dlpmr149"}
{"time":"2025-05-28T22:08:11.625173213Z","level":"INFO","msg":"writer: Do: started","stream_id":"dlpmr149"}
{"time":"2025-05-28T22:08:11.625180978Z","level":"INFO","msg":"sender: started","stream_id":"dlpmr149"}
{"time":"2025-05-28T22:08:11.625205016Z","level":"INFO","msg":"handler: started","stream_id":"dlpmr149"}
{"time":"2025-05-28T22:08:11.910623852Z","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-05-28T22:14:57.045138344Z","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-05-28T22:22:25.450826941Z","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/9petrestaurant-huazhong-university-of-science-and-technology/GraphLLM4CTR_Hyperparameter_Temperature_CTR_bookcrossing/dlpmr149/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-05-28T22:22:33.11820006Z","level":"INFO","msg":"api: retrying HTTP error","status":500,"url":"https://api.wandb.ai/files/9petrestaurant-huazhong-university-of-science-and-technology/GraphLLM4CTR_Hyperparameter_Temperature_CTR_bookcrossing/dlpmr149/file_stream","body":"{\"error\":\"context deadline exceeded\"}"}
{"time":"2025-05-28T22:22:42.049174444Z","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-05-29T02:47:06.178911361Z","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/9petrestaurant-huazhong-university-of-science-and-technology/GraphLLM4CTR_Hyperparameter_Temperature_CTR_bookcrossing/dlpmr149/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
