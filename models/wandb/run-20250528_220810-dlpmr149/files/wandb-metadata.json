{"os": "Linux-5.4.0-216-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.19", "startedAt": "2025-05-28T22:08:10.947933Z", "args": ["--train_path", "/data/datasets/processed_datasets/amazon/train.csv", "--val_path", "/data/datasets/processed_datasets/amazon/val.csv", "--test_path", "/data/datasets/processed_datasets/amazon/test.csv", "--embedding_dir", "/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.07/aligned_embeddings_epoch1", "--item_meta_path", "/data/datasets/processed_datasets/amazon/train.csv", "--qformer_ckpt", "/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.07/qformer_epoch1.pt", "--epochs", "10", "--batch_size", "128", "--lr", "5e-4", "--weight_decay", "1e-5", "--early_stop_patience", "5", "--warmup_steps", "500", "--max_grad_norm", "1.0", "--seed", "42", "--log_dir", "/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/ctr_results/ctr_temp_0.07", "--device", "cuda", "--wandb_name", "ctr_amazon_temp_0.07", "--dataset_type", "amazon"], "program": "train_ctr_round3.py", "codePath": "models/train_ctr_round3.py", "git": {"remote": "**************:gaoshan-code/GraphLLM4CTR.git", "commit": "1429a67a167bd0ff04ab2b0c0b33b335b0328dde"}, "email": "<EMAIL>", "root": "/root/code/GraphLLM4CTR/models", "host": "vmInstancekdtfa3ig", "executable": "/root/miniconda3/envs/py38/bin/python", "codePathLocal": "train_ctr_round3.py", "cpu_count": 16, "cpu_count_logical": 16, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 1, "disk": {"/": {"total": "51835101184", "used": "41948139520"}}, "memory": {"total": "120219561984"}, "cpu": {"count": 16, "countLogical": 16}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.6"}