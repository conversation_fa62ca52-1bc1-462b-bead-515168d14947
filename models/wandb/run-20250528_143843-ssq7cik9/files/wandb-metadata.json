{"os": "Linux-5.4.0-216-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.19", "startedAt": "2025-05-28T14:38:43.578587Z", "args": ["--data_dir", "/data/datasets/processed_datasets/amazon", "--model_save_dir", "/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_analysis_20250528_125630/temp_0.7", "--batch_size", "512", "--num_epochs", "1", "--learning_rate", "1e-3", "--device", "cuda", "--temperature", "0.7", "--wandb_project", "GraphLLM4CTR_Hyperparameter_Temperature_Amazon_contrastive_FAST", "--dataset_type", "amazon"], "program": "contrastive_learning_train_for_check.py", "codePath": "models/contrastive_learning_train_for_check.py", "git": {"remote": "**************:gaoshan-code/GraphLLM4CTR.git", "commit": "1429a67a167bd0ff04ab2b0c0b33b335b0328dde"}, "email": "<EMAIL>", "root": "/root/code/GraphLLM4CTR/models", "host": "vmInstancekdtfa3ig", "executable": "/root/miniconda3/envs/py38/bin/python", "codePathLocal": "contrastive_learning_train_for_check.py", "cpu_count": 16, "cpu_count_logical": 16, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 1, "disk": {"/": {"total": "51835101184", "used": "41900482560"}}, "memory": {"total": "120219561984"}, "cpu": {"count": 16, "countLogical": 16}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.6"}