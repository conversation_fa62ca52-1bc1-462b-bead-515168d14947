Some weights of RobertaModel were not initialized from the model checkpoint at /root/code/roberta-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/root/code/GraphLLM4CTR/models/embedding_loader.py:54: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  state = torch.load(qformer_checkpoint_path, map_location=device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/embedding_loader.py:76: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  emb_tensor = torch.load(emb_path, map_location=self.device)
Traceback (most recent call last):
  File "hyperparameter_analysis_embedding.py", line 558, in <module>
    results = run_embedding_size_analysis(args)
  File "hyperparameter_analysis_embedding.py", line 421, in run_embedding_size_analysis
    results = train_and_evaluate(
  File "hyperparameter_analysis_embedding.py", line 149, in train_and_evaluate
    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 70, in __init__
    self.item_imp = self._load_embeddings(embedding_dir, "item_impressed_proj", is_user=False)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 76, in _load_embeddings
    emb_tensor = torch.load(emb_path, map_location=self.device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1097, in load
    return _load(
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1525, in _load
    result = unpickler.load()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1492, in persistent_load
    typed_storage = load_tensor(dtype, nbytes, key, _maybe_decode_ascii(location))
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1466, in load_tensor
    wrap_storage=restore_location(storage, location),
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1389, in restore_location
    return default_restore_location(storage, map_location)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 414, in default_restore_location
    result = fn(storage, location)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 392, in _deserialize
    return obj.to(device=device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/storage.py", line 187, in to
    return _to(self, device, non_blocking)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/_utils.py", line 89, in _to
    untyped_storage = torch.UntypedStorage(self.size(), device=device)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 3.58 GiB. GPU 0 has a total capacity of 23.55 GiB of which 1.94 GiB is free. Process 54155 has 8.30 GiB memory in use. Including non-PyTorch memory, this process has 13.29 GiB memory in use. Of the allocated memory 12.72 GiB is allocated by PyTorch, and 203.10 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
Traceback (most recent call last):
  File "hyperparameter_analysis_embedding.py", line 558, in <module>
    results = run_embedding_size_analysis(args)
  File "hyperparameter_analysis_embedding.py", line 421, in run_embedding_size_analysis
    results = train_and_evaluate(
  File "hyperparameter_analysis_embedding.py", line 149, in train_and_evaluate
    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 70, in __init__
    self.item_imp = self._load_embeddings(embedding_dir, "item_impressed_proj", is_user=False)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 76, in _load_embeddings
    emb_tensor = torch.load(emb_path, map_location=self.device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1097, in load
    return _load(
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1525, in _load
    result = unpickler.load()
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1492, in persistent_load
    typed_storage = load_tensor(dtype, nbytes, key, _maybe_decode_ascii(location))
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1466, in load_tensor
    wrap_storage=restore_location(storage, location),
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1389, in restore_location
    return default_restore_location(storage, map_location)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 414, in default_restore_location
    result = fn(storage, location)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 392, in _deserialize
    return obj.to(device=device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/storage.py", line 187, in to
    return _to(self, device, non_blocking)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/_utils.py", line 89, in _to
    untyped_storage = torch.UntypedStorage(self.size(), device=device)
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 3.58 GiB. GPU 0 has a total capacity of 23.55 GiB of which 1.94 GiB is free. Process 54155 has 8.30 GiB memory in use. Including non-PyTorch memory, this process has 13.29 GiB memory in use. Of the allocated memory 12.72 GiB is allocated by PyTorch, and 203.10 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
