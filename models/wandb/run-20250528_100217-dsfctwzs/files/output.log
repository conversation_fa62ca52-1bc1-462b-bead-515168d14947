2025-05-28 10:02:18,309 - __main__ - INFO - Loading dataset and dataloader
2025-05-28 10:02:21,063 - __main__ - INFO - Initializing batch builder

=== Initializing OptimizedBatchBuilder ===
Data directory: /data/datasets/processed_datasets/amazon
Dataset type: amazon
Batch size: 512
Device: cuda
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:158: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_user_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:160: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_item_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:162: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:164: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:166: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:168: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:173: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:174: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.click_matrix = torch.load(f"{graph_matrix_dir}/click_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:63: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.impression_matrix = torch.load(f"{graph_matrix_dir}/impression_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:64: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1 = torch.load(f"{graph_matrix_dir}/user_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:65: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2 = torch.load(f"{graph_matrix_dir}/user_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:66: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1 = torch.load(f"{graph_matrix_dir}/item_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:67: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2 = torch.load(f"{graph_matrix_dir}/item_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:44: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:45: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type2.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:46: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:47: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type2.pt")
2025-05-28 10:02:27,724 - __main__ - INFO - Initializing contrastive learning model
contrastive_learning_train_for_check.py:217: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
2025-05-28 10:02:28,127 - __main__ - INFO - Starting training
  0%|          | 0/1 [00:00<?, ?it/s]              contrastive_learning_train_for_check.py:256: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():        | 0/2124 [00:00<?, ?it/s]
Lambda values: λ1=1.0000, λ2=1.0000, λ3=1.0000, λ4=1.0000, λ5=1.0000
[Debug] Raw Grad Norm: inf
[Debug] Grad Norm: nan | Update Norm: nan
[Debug] Update Ratio: nan
/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/optim/lr_scheduler.py:216: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn(
2025-05-28 10:02:29,970 - __main__ - INFO - Batch 0/2124, GPU Memory: 1.80GB
2025-05-28 10:02:29,971 - __main__ - INFO -   user_edge_type1: nnz=20
2025-05-28 10:02:29,971 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:02:29,971 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:29,971 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   0%|          | 8/2124 [00:05<21:32,  1.64it/s]  
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3462
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3422
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3356
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3554
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3344
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3270
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3307
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3267
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3470
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7999, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3340
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:02:35,779 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:02:35,779 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:35,779 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   1%|          | 18/2124 [00:11<19:37,  1.79it/s]
Lambda values: λ1=0.7999, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3347
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3289
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3368
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3327
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7998, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3354
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7997, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3419
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7997, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3446
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3411
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3497
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7996, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3594
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:02:41,340 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:02:41,341 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:41,341 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   1%|▏         | 28/2124 [00:17<21:12,  1.65it/s]
Lambda values: λ1=0.7995, λ2=0.7995, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3613
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7995, λ2=0.7995, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3751
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7994, λ2=0.7994, λ3=0.7994, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3765
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7994, λ2=0.7994, λ3=0.7994, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3762
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7993, λ2=0.7993, λ3=0.7993, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3825
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7992, λ2=0.7992, λ3=0.7992, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3860
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7992, λ2=0.7992, λ3=0.7992, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3899
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7991, λ2=0.7991, λ3=0.7991, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3990
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7990, λ2=0.7990, λ3=0.7990, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4011
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7990, λ2=0.7990, λ3=0.7990, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4032
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:02:47,089 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:02:47,089 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:47,090 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   2%|▏         | 38/2124 [00:22<19:31,  1.78it/s]
Lambda values: λ1=0.7989, λ2=0.7989, λ3=0.7989, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4105
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7988, λ2=0.7988, λ3=0.7988, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4101
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7988, λ2=0.7988, λ3=0.7988, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4120
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7987, λ2=0.7987, λ3=0.7987, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4184
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7986, λ2=0.7986, λ3=0.7986, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4198
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7985, λ2=0.7985, λ3=0.7985, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4231
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7984, λ2=0.7984, λ3=0.7984, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4230
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7984, λ2=0.7984, λ3=0.7984, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4250
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7983, λ2=0.7983, λ3=0.7983, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4284
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7982, λ2=0.7982, λ3=0.7982, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4354
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:02:52,701 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:02:52,702 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:52,702 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   2%|▏         | 48/2124 [00:28<19:23,  1.78it/s]
Lambda values: λ1=0.7981, λ2=0.7981, λ3=0.7981, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4377
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7980, λ2=0.7980, λ3=0.7980, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4346
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7979, λ2=0.7979, λ3=0.7979, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4391
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7978, λ2=0.7978, λ3=0.7978, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4362
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7977, λ2=0.7977, λ3=0.7977, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4412
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7976, λ2=0.7976, λ3=0.7976, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4427
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7975, λ2=0.7975, λ3=0.7975, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4509
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7974, λ2=0.7974, λ3=0.7974, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4477
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7973, λ2=0.7973, λ3=0.7973, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4490
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7972, λ2=0.7971, λ3=0.7972, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4481
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:02:58,511 - __main__ - INFO -   user_edge_type2: nnz=19
2025-05-28 10:02:58,511 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:02:58,511 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   3%|▎         | 58/2124 [00:34<20:12,  1.70it/s]
Lambda values: λ1=0.7970, λ2=0.7970, λ3=0.7970, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4534
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7969, λ2=0.7969, λ3=0.7969, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4550
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7968, λ2=0.7968, λ3=0.7968, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4545
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7967, λ2=0.7967, λ3=0.7967, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4566
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7965, λ2=0.7965, λ3=0.7966, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4611
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7964, λ2=0.7964, λ3=0.7964, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4629
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7963, λ2=0.7963, λ3=0.7963, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4640
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7962, λ2=0.7961, λ3=0.7962, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4607
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7960, λ2=0.7960, λ3=0.7960, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4673
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7959, λ2=0.7959, λ3=0.7959, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4670
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:03:04,328 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:03:04,328 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:03:04,328 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   3%|▎         | 68/2124 [00:40<19:10,  1.79it/s]
Lambda values: λ1=0.7958, λ2=0.7957, λ3=0.7958, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4658
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7956, λ2=0.7956, λ3=0.7956, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4703
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7955, λ2=0.7955, λ3=0.7955, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4693
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7953, λ2=0.7953, λ3=0.7953, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4671
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7952, λ2=0.7952, λ3=0.7952, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4732
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7950, λ2=0.7950, λ3=0.7950, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4746
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7949, λ2=0.7949, λ3=0.7949, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4738
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7947, λ2=0.7947, λ3=0.7947, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4804
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7946, λ2=0.7945, λ3=0.7946, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4808
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7944, λ2=0.7944, λ3=0.7944, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4805
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:03:09,940 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:03:09,940 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:03:09,940 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   4%|▎         | 78/2124 [00:45<18:55,  1.80it/s]
Lambda values: λ1=0.7942, λ2=0.7942, λ3=0.7942, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4852
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7941, λ2=0.7941, λ3=0.7941, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4842
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7939, λ2=0.7939, λ3=0.7939, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4870
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7937, λ2=0.7937, λ3=0.7937, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4887
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7936, λ2=0.7935, λ3=0.7936, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4846
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7934, λ2=0.7934, λ3=0.7934, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4896
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7932, λ2=0.7932, λ3=0.7932, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4923
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7930, λ2=0.7930, λ3=0.7931, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4931
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7929, λ2=0.7928, λ3=0.7929, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4977
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7927, λ2=0.7927, λ3=0.7927, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4990
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-28 10:03:15,497 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-28 10:03:15,497 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-28 10:03:15,497 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   4%|▍         | 88/2124 [00:51<20:28,  1.66it/s]
Lambda values: λ1=0.7925, λ2=0.7925, λ3=0.7925, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4969
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7923, λ2=0.7923, λ3=0.7923, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5023
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7921, λ2=0.7921, λ3=0.7921, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5094
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7919, λ2=0.7919, λ3=0.7919, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5089
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7917, λ2=0.7917, λ3=0.7917, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5095
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7915, λ2=0.7915, λ3=0.7915, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5089
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7913, λ2=0.7913, λ3=0.7913, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5122
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7911, λ2=0.7911, λ3=0.7911, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5191
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7909, λ2=0.7909, λ3=0.7909, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5169
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.7907, λ2=0.7907, λ3=0.7907, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5233
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
