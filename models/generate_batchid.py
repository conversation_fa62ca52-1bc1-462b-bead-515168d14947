import torch
from torch.utils.data import DataLoader
from optimized_batch_builder import OptimizedBatchBuilder
import pandas as pd
from tqdm import tqdm
import os

class ContrastiveDataset(torch.utils.data.Dataset):
    def __init__(self, data_path):
        self.data = pd.read_csv(data_path)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        return {
            'user_id': row['user_id'],
            'item_id': row['movie_id'],
            'label': row['label']
        }

def generate_batch_id_lists(data_dir, batch_size, device):
    dataset = ContrastiveDataset(os.path.join(data_dir, "train.csv"))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    batch_builder = OptimizedBatchBuilder(data_dir, batch_size, device)

    all_user_ids = []
    all_item_ids = []

    print("Generating user_id_batches.pt and item_id_batches.pt")
    for batch_data in tqdm(dataloader):
        user_ids = batch_data['user_id']
        item_ids = batch_data['item_id']
        label = batch_data['label']

        # 记录 user/item id（无需 build_batch 实际返回）
        all_user_ids.append(user_ids.clone().cpu())
        all_item_ids.append(item_ids.clone().cpu())

    torch.save(all_user_ids, os.path.join(data_dir, "user_id_batches.pt"))
    torch.save(all_item_ids, os.path.join(data_dir, "item_id_batches.pt"))
    print("Saved user_id_batches.pt and item_id_batches.pt")

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_dir", type=str, required=True)
    parser.add_argument("--batch_size", type=int, default=32)
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    args = parser.parse_args()

    generate_batch_id_lists(args.data_dir, args.batch_size, args.device)
