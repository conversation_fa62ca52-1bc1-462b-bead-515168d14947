#!/bin/bash

# Run the third round of CTR model improvements (residual connections and layer normalization)
python train_ctr_round3.py \
  --train_path /data/datasets/processed_datasets/movielens/train.csv \
  --val_path /data/datasets/processed_datasets/movielens/val.csv \
  --test_path /data/datasets/processed_datasets/movielens/test.csv \
  --embedding_dir /root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1 \
  --item_meta_path /data/datasets/processed_datasets/movielens/train.csv \
  --qformer_ckpt /root/code/GraphLLM4CTR/models/contrastive_model_tmp/qformer_epoch1.pt \
  --epochs 10 \
  --batch_size 256 \
  --lr 5e-4 \
  --weight_decay 1e-5 \
  --early_stop_patience 5 \
  --warmup_steps 500 \
  --max_grad_norm 1.0 \
  --seed 42 \
  --log_dir ./ctr_logs_round3 \
  --device cuda
