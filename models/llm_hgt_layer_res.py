import torch
import torch.nn as nn
from torch_geometric.nn import MessagePassing
from llm_message_update import LLMMessageUpdater

class ResLLMHGTLayer(MessagePassing):
    def __init__(self, input_dim, hidden_dim, num_types, layer_idx, num_layers, num_heads=4, dropout=0.1, device="cpu"):
        super().__init__(aggr="add")
        self.device = device
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        self.layer_idx = layer_idx
        self.dropout = dropout
        self.proj_layers = nn.ModuleDict()

        # For the first layer, use the original input dimensions
        if layer_idx == 0:
            # Node type 0 (user): input_dim = 256
            # Node type 1 (item): input_dim = 1024 (256 + 768)
            self.proj_layers['0'] = nn.Linear(256, hidden_dim)
            self.proj_layers['1'] = nn.Linear(1024, hidden_dim)
        else:
            # For subsequent layers, both user and item features have hidden_dim dimensions
            self.proj_layers['0'] = nn.Linear(hidden_dim, hidden_dim)
            self.proj_layers['1'] = nn.Linear(hidden_dim, hidden_dim)

        # Use the correct dimensions for the linear layers
        self.k_linears = nn.ModuleList()
        self.q_linears = nn.ModuleList()
        self.v_linears = nn.ModuleList()

        for i in range(num_types):
            # Use hidden_dim as input dimension since we've already projected to hidden_dim
            self.k_linears.append(nn.Linear(hidden_dim, hidden_dim))
            self.q_linears.append(nn.Linear(hidden_dim, hidden_dim))
            self.v_linears.append(nn.Linear(hidden_dim, hidden_dim))

        self.a_linears = nn.ModuleList([nn.Linear(hidden_dim, hidden_dim) for _ in range(num_types)])

        # Layer normalization for each node type
        self.norms = nn.ModuleList([nn.LayerNorm(hidden_dim) for _ in range(num_types)])

        self.skip = nn.Parameter(torch.ones(num_types))
        self.type_dict = {}

        self.msg_updater = LLMMessageUpdater(
            llm_model_name="roberta-base",
            gnn_num_layers=num_layers,
            llm_num_layers=12,
            layer_idx=layer_idx,
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            device=device
        )

        # Dropout layer
        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, x_dict, edge_index, edge_type, node_type):
        self.type_dict = node_type  # Save node_type info

        # Ensure edge_index and node_type are on the correct device
        edge_index = edge_index.to(self.device)
        node_type = node_type.to(self.device)

        # Store original input for residual connection
        if isinstance(x_dict, torch.Tensor):
            # If it's a tensor, split it back into a dictionary based on node types
            num_users = torch.sum(node_type == 0).item()
            user_features = x_dict[:num_users]
            item_features = x_dict[num_users:]
            original_dict = {0: user_features.clone(), 1: item_features.clone()}
            x_dict = {0: user_features, 1: item_features}
        else:
            # If it's already a dictionary, make a copy
            original_dict = {k: v.clone() for k, v in x_dict.items()}

        # Project each node type
        for ntype, x in x_dict.items():
            x = x.to(self.device)
            x = self.proj_layers[str(ntype)](x)
            x_dict[ntype] = x

        # Concatenate all node type vectors
        x_all = torch.cat([x for x in x_dict.values()], dim=0)

        # Call propagate process
        message_output = self.propagate(edge_index, x=x_all, edge_type=edge_type)

        # Split the output back into user and item features
        num_users = torch.sum(node_type == 0).item()
        user_output = message_output[:num_users]
        item_output = message_output[num_users:]

        # Apply residual connection for each node type if not the first layer
        if self.layer_idx > 0:
            # For users (already have same dimension)
            user_output = user_output + original_dict[0]

            # For items (need to project if first layer)
            if self.layer_idx == 1 and original_dict[1].shape[-1] != user_output.shape[-1]:
                if not hasattr(self, 'item_proj'):
                    self.item_proj = nn.Linear(original_dict[1].shape[-1], user_output.shape[-1]).to(self.device)
                item_residual = self.item_proj(original_dict[1])
            else:
                item_residual = original_dict[1]

            item_output = item_output + item_residual

        # Apply layer normalization
        user_output = self.norms[0](user_output)
        item_output = self.norms[1](item_output)

        # Apply dropout
        user_output = self.dropout_layer(user_output)
        item_output = self.dropout_layer(item_output)

        # Recombine into a single tensor
        output = torch.cat([user_output, item_output], dim=0)

        return output

    def message(self, x_j, x_i, edge_type, edge_index_i=None, edge_index_j=None):
        # Ensure input tensors are on the correct device
        x_j = x_j.to(self.device)
        x_i = x_i.to(self.device)

        # For simplicity, use fixed node types
        # In a user-item bipartite graph:
        # - Source nodes (edge_index_j) are users (type 0)
        # - Target nodes (edge_index_i) are items (type 1)
        source_type = 0
        target_type = 1

        k = self.k_linears[source_type](x_j).view(-1, self.num_heads, self.head_dim)
        q = self.q_linears[target_type](x_i).view(-1, self.num_heads, self.head_dim)
        v = self.v_linears[source_type](x_j).view(-1, self.num_heads, self.head_dim)

        alpha = (q * k).sum(dim=-1) / (self.head_dim ** 0.5)
        alpha = torch.softmax(alpha, dim=1).unsqueeze(-1)

        # Apply dropout to attention weights
        alpha = nn.functional.dropout(alpha, p=self.dropout, training=self.training)

        out = (alpha * v).view(-1, self.head_dim * self.num_heads)

        return self.msg_updater(out)

    def update(self, aggr_out):
        # Ensure output tensor is on the correct device
        return aggr_out.to(self.device)


class ResLLMHGTEncoder(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, num_types, num_heads=4, dropout=0.1, device="cpu"):
        super().__init__()
        self.device = device
        self.hidden_dim = hidden_dim
        self.layers = nn.ModuleList()

        for l in range(num_layers):
            self.layers.append(ResLLMHGTLayer(
                input_dim if l == 0 else hidden_dim,
                hidden_dim,
                num_types,
                layer_idx=l,
                num_layers=num_layers,
                num_heads=num_heads,
                dropout=dropout,
                device=device
            ))

        # Final layer normalization
        self.final_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x_dict, edge_index, edge_type, node_type):
        # Ensure input tensors are on the correct device
        edge_index = edge_index.to(self.device)
        node_type = node_type.to(self.device)

        # Store original input for deep residual connection
        if isinstance(x_dict, dict):
            # If it's a dictionary, we need to project to the same dimension
            if 0 in x_dict and 1 in x_dict:
                user_feat = x_dict[0].to(self.device)
                item_feat = x_dict[1].to(self.device)

                # Project if needed
                if user_feat.shape[-1] != self.hidden_dim:
                    if not hasattr(self, 'user_proj'):
                        self.user_proj = nn.Linear(user_feat.shape[-1], self.hidden_dim).to(self.device)
                    user_feat = self.user_proj(user_feat)

                if item_feat.shape[-1] != self.hidden_dim:
                    if not hasattr(self, 'item_proj'):
                        self.item_proj = nn.Linear(item_feat.shape[-1], self.hidden_dim).to(self.device)
                    item_feat = self.item_proj(item_feat)

                # Store for deep residual
                original_x = torch.cat([user_feat, item_feat], dim=0)
            else:
                original_x = None
        else:
            # If it's already a tensor
            original_x = x_dict.to(self.device)
            if original_x.shape[-1] != self.hidden_dim:
                if not hasattr(self, 'input_proj'):
                    self.input_proj = nn.Linear(original_x.shape[-1], self.hidden_dim).to(self.device)
                original_x = self.input_proj(original_x)

        # Process through each layer
        layer_output = x_dict
        for layer in self.layers:
            layer_output = layer(layer_output, edge_index, edge_type, node_type)

        # Add deep residual connection if possible
        if original_x is not None:
            layer_output = layer_output + original_x

        # Apply final layer normalization
        layer_output = self.final_norm(layer_output)

        return layer_output
