import torch


def build_rgcn_input(user_ids, item_ids, loader):
    """
    构造用于 R-GCN 的输入图结构：节点特征、边索引、边类型

    返回：
        x: Tensor [N, D]
        edge_index: LongTensor [2, E]
        edge_type: LongTensor [E]
    """
    device = loader.device

    # Get user and item embeddings and ensure they're on the correct device and data type
    user_x = loader.get_user_rgcn_input(user_ids).to(device).float()  # [U, D] or [U, T, D]
    item_x = loader.get_item_rgcn_input(item_ids).to(device).float()  # [I, D] or [I, T, D]

    # Reshape to 2D if needed
    if len(user_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        # print(f"Reshaping user_x from {user_x.shape} to [batch_size, hidden_dim]")
        user_x = user_x[:, 0, :]  # Take the first token's embedding

    if len(item_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        # print(f"Reshaping item_x from {item_x.shape} to [batch_size, hidden_dim]")
        item_x = item_x[:, 0, :]  # Take the first token's embedding

    x = torch.cat([user_x, item_x], dim=0)         # [U+I, D]

    num_users = len(user_ids)

    edge_index = []
    edge_type = []

    # Create a sparse connection pattern to reduce memory usage
    # Connect each user to a subset of items
    for i, uid in enumerate(user_ids):
        for j, iid in enumerate(item_ids):
            if j % 10 == 0:  # Connect to every 10th item to reduce edge count
                edge_index.append([i, num_users + j])
                edge_type.append(0)  # 关系类型 0：点击

    edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
    edge_type = torch.LongTensor(edge_type).to(device)

    return x, edge_index, edge_type


def build_hgt_input(user_ids, item_ids, loader):
    # print("\n=== Debug: build_hgt_input ===")
    # print(f"Number of user_ids: {len(user_ids)}")
    # print(f"Number of item_ids: {len(item_ids)}")

    device = loader.device
    x_dict = {}
    node_type = {}
    global_idx = 0

    uid_to_gid = {}
    iid_to_gid = {}

    # Get user features and ensure they're on the correct device and data type
    user_feats = loader.get_user_hgt_input(user_ids).to(device).float()
    # print(f"User features shape: {user_feats.shape}, dtype: {user_feats.dtype}, device: {user_feats.device}")

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    # This simplifies the model by using only the first token's representation
    if len(user_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        # print(f"Reshaping user features from {user_feats.shape} to [batch_size, hidden_dim]")
        user_feats = user_feats[:, 0, :]  # Take the first token's embedding

    x_dict[0] = user_feats
    for uid in user_ids:
        uid = int(uid)
        node_type[global_idx] = 0
        uid_to_gid[uid] = global_idx
        global_idx += 1

    # Get item features and ensure they're on the correct device and data type
    item_feats = loader.get_item_hgt_input(item_ids).to(device).float()
    # print(f"Item features shape: {item_feats.shape}, dtype: {item_feats.dtype}, device: {item_feats.device}")

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    # This simplifies the model by using only the first token's representation
    if len(item_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        # print(f"Reshaping item features from {item_feats.shape} to [batch_size, hidden_dim]")
        item_feats = item_feats[:, 0, :]  # Take the first token's embedding

    x_dict[1] = item_feats
    for iid in item_ids:
        # Handle both integer and string item IDs (BookCrossing has string IDs)
        if isinstance(iid, (int, float)):
            iid = int(iid)
        else:
            iid = str(iid)  # Keep as string for BookCrossing dataset
        node_type[global_idx] = 1
        iid_to_gid[iid] = global_idx
        global_idx += 1

    edge_index = []
    edge_type = []

    # print(f"Total nodes: {global_idx} (Users: {len(user_ids)}, Items: {len(item_ids)})")

    # Create a sparse connection pattern to reduce memory usage
    # Connect each user to a subset of items
    for uid in user_ids:
        for j, iid in enumerate(item_ids):
            if j % 10 == 0:  # Connect to every 10th item to reduce edge count
                u_idx = uid_to_gid[int(uid)]
                # Handle both integer and string item IDs
                if isinstance(iid, (int, float)):
                    lookup_iid = int(iid)
                else:
                    lookup_iid = str(iid)
                i_idx = iid_to_gid[lookup_iid]
                edge_index.append([u_idx, i_idx])
                # Use a single integer for edge type
                edge_type.append(0)  # user -> item (use a single type)

    # print(f"Number of edges created: {len(edge_index)}")

    # Convert to tensors and move to the correct device
    edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
    edge_type = torch.LongTensor(edge_type).to(device)
    node_type_tensor = torch.tensor([node_type[i] for i in range(len(node_type))], dtype=torch.long).to(device)

    # print(f"edge_index shape: {edge_index.shape}, dtype: {edge_index.dtype}, device: {edge_index.device}")
    # print(f"edge_type shape: {edge_type.shape}, dtype: {edge_type.dtype}, device: {edge_type.device}")
    # print(f"node_type_tensor shape: {node_type_tensor.shape}, dtype: {node_type_tensor.dtype}, device: {node_type_tensor.device}")

    # Check if edge indices are valid
    max_node_idx = global_idx - 1
    max_edge_idx = edge_index.max().item()
    # print(f"Max node index: {max_node_idx}, Max edge index: {max_edge_idx}")
    if max_edge_idx > max_node_idx:
        print(f"WARNING: Edge index {max_edge_idx} exceeds max node index {max_node_idx}")
        # Limit edge indices to valid range
        edge_index = torch.clamp(edge_index, 0, max_node_idx)

    return x_dict, edge_index, edge_type, node_type_tensor

