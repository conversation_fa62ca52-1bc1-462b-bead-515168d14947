import os
import torch
import pandas as pd
from torch import nn
from transformers import AutoTokenizer, AutoModel

class RobertaTextEncoder(nn.Module):
    def __init__(self, tokenizer, model, device="cpu"):
        super().__init__()
        self.tokenizer = tokenizer
        self.model = model
        self.device = device
        self.model.eval()

    def forward(self, texts):
        tokens = self.tokenizer(
            texts,
            padding=True,
            truncation=True,
            max_length=128,
            return_tensors="pt"
        ).to(self.device)

        with torch.no_grad():
            outputs = self.model(**tokens)
            mask = tokens['attention_mask'].unsqueeze(-1)
            embeddings = (outputs.last_hidden_state * mask).sum(dim=1) / mask.sum(dim=1)
        return embeddings


class EmbeddingLoader:
    def __init__(
        self,
        embedding_dir,
        item_meta_path,
        item_id_col,
        title_col,
        text_qformer,
        qformer_checkpoint_path,
        text_tokenizer,
        device="cpu"
    ):
        self.device = device
        self.text_qformer = text_qformer.to(device).eval()
        self.tokenizer = text_tokenizer
        self.text_encoder = RobertaTextEncoder(
            tokenizer=text_tokenizer,
            model=AutoModel.from_pretrained("/root/code/roberta-base").to(device),
            device=device
        )

        # Load text-q-former weights
        if qformer_checkpoint_path:
            state = torch.load(qformer_checkpoint_path, map_location=device)
            if "model_state_dict" in state:
                state = state["model_state_dict"]
            state = {k.replace("text_qformer.", ""): v for k, v in state.items() if "text_qformer." in k}
            self.text_qformer.load_state_dict(state, strict=False)

        # Load item metadata
        meta = pd.read_csv(item_meta_path)[[item_id_col, title_col]].dropna()
        self.item_title_map = dict(zip(meta[item_id_col], meta[title_col]))

        # Load all graph embeddings
        self.user_graph = self._load_embeddings(embedding_dir, "user_graph_proj", is_user=True)
        self.item_graph = self._load_embeddings(embedding_dir, "item_graph_proj", is_user=False)
        self.user_click = self._load_embeddings(embedding_dir, "user_click_proj", is_user=True)
        self.user_imp = self._load_embeddings(embedding_dir, "user_impression_proj", is_user=True)
        self.item_click = self._load_embeddings(embedding_dir, "item_clicked_proj", is_user=False)
        self.item_imp = self._load_embeddings(embedding_dir, "item_impressed_proj", is_user=False)

    def _load_embeddings(self, embedding_dir, emb_type, is_user=True):
        emb_path = os.path.join(embedding_dir, f"{emb_type}.pt")
        map_path = os.path.join(embedding_dir, f"{emb_type}_id_mapping.csv")

        emb_tensor = torch.load(emb_path, map_location=self.device)
        id_col = "user_id" if is_user else "item_id"
        id_map = pd.read_csv(map_path).set_index(id_col)["embedding_idx"].to_dict()

        return {"tensor": emb_tensor, "id_map": id_map}

    def _get_embedding(self, ids, emb_dict):
        embs = []
        for _id in ids:
            # Handle tensor inputs by converting to Python types first
            if isinstance(_id, torch.Tensor):
                if _id.numel() == 1:  # Single element tensor
                    _id = _id.item()
                else:
                    raise ValueError(f"Expected single element tensor, got tensor with {_id.numel()} elements")

            # Keep original ID format (don't convert strings to int)
            # User IDs are integers, Item IDs are strings in BookCrossing
            if isinstance(_id, (int, float)):
                _id = int(_id)
            else:
                _id = str(_id)  # Keep as string for item IDs

            idx = emb_dict["id_map"].get(_id)
            if idx is None:
                # Use a default embedding (zeros) for missing IDs
                print(f"Warning: Missing embedding for id {_id}, using default embedding")
                default_emb = torch.zeros_like(emb_dict["tensor"][0])
                embs.append(default_emb)
            else:
                embs.append(emb_dict["tensor"][idx])
        return torch.stack(embs).to(self.device)

    def get_user_hgt_input(self, user_ids):
        return self._get_embedding(user_ids, self.user_graph)

    def get_item_hgt_input(self, item_ids):
        graph_emb = self._get_embedding(item_ids, self.item_graph)
        titles = []
        for iid in item_ids:
            # Handle tensor inputs by converting to Python types first
            if isinstance(iid, torch.Tensor):
                if iid.numel() == 1:  # Single element tensor
                    iid = iid.item()
                else:
                    raise ValueError(f"Expected single element tensor, got tensor with {iid.numel()} elements")

            # Handle string item IDs properly
            if isinstance(iid, (int, float)):
                lookup_id = int(iid)
            else:
                lookup_id = str(iid)

            title = self.item_title_map.get(lookup_id, "Unknown Title")
            if title == "Unknown Title":
                print(f"Warning: Missing title for item {iid}, using default")
            titles.append(title)
        title_emb = self._encode_titles(titles)
        return torch.cat([graph_emb, title_emb], dim=-1)

    def get_user_rgcn_input(self, user_ids):
        click = self._get_embedding(user_ids, self.user_click)
        imp = self._get_embedding(user_ids, self.user_imp)
        return (click + imp) / 2

    def get_item_rgcn_input(self, item_ids):
        click = self._get_embedding(item_ids, self.item_click)
        imp = self._get_embedding(item_ids, self.item_imp)
        return (click + imp) / 2

    def _encode_titles(self, titles):
        with torch.no_grad():
            base_emb = self.text_encoder(titles)                     # [B, 768]
            qformer_out = self.text_qformer(base_emb.unsqueeze(1))   # [B, 1, 768]
        return qformer_out.squeeze(1)                                 # [B, 768]
