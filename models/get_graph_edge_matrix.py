import torch
import os
import argparse

def extract_graph_edge_matrices(dataset_type='bookcrossing'):
    """
    Extract edge matrices from graph files for the specified dataset

    Args:
        dataset_type (str): 'movielens' or 'bookcrossing'
    """
    # 设置数据目录
    if dataset_type == 'movielens':
        data_dir = "/data/datasets/processed_datasets/movielens"
    elif dataset_type == 'bookcrossing':
        data_dir = "/data/datasets/processed_datasets/bookcrossing"
    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}")

    graph_dir = os.path.join(data_dir, "graph")
    matrix_dir = os.path.join(data_dir, "graph_edge_matrix")
    os.makedirs(matrix_dir, exist_ok=True)

    print(f"Processing {dataset_type} dataset")
    print(f"Graph directory: {graph_dir}")
    print(f"Output directory: {matrix_dir}")

    # 定义图文件和对应的边类型
    graph_files = {
        os.path.join(graph_dir, "user_item_graph.pt"): [
            ("user", "click", "item", "click_matrix.pt"),
            ("user", "impression", "item", "impression_matrix.pt"),
        ],
        os.path.join(graph_dir, "user_graph.pt"): [
            ("user", "co_click", "user", "user_edge_type1.pt"),
            ("user", "co_impression", "user", "user_edge_type2.pt"),
        ],
        os.path.join(graph_dir, "item_graph.pt"): [
            ("item", "co_clicked", "item", "item_edge_type1.pt"),
            ("item", "co_impressed", "item", "item_edge_type2.pt"),
        ]
    }

    # 处理每个图文件
    for graph_file, edge_types in graph_files.items():
        if not os.path.exists(graph_file):
            print(f"Warning: Graph file {graph_file} not found, skipping...")
            continue

        print(f"Loading {graph_file}...")
        try:
            graph = torch.load(graph_file, weights_only=False)
        except Exception as e:
            print(f"Error loading {graph_file}: {e}")
            continue

        for src, rel, dst, save_name in edge_types:
            if (src, rel, dst) not in graph.edge_types:
                print(f"  Edge type ({src}, {rel}, {dst}) not found in {graph_file}")
                continue

            edge_data = graph[(src, rel, dst)]
            if hasattr(edge_data, 'edge_index'):
                edge_index = edge_data.edge_index
                save_path = os.path.join(matrix_dir, save_name)
                print(f"  Saving {save_name}, shape={edge_index.shape}")
                torch.save(edge_index, save_path)
            else:
                print(f"  No edge_index in ({src}, {rel}, {dst})")

    print(f"\nAll matrices saved to {matrix_dir}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract graph edge matrices")
    parser.add_argument("--dataset_type", type=str, default="bookcrossing",
                       choices=["movielens", "bookcrossing"],
                       help="Dataset type (movielens or bookcrossing)")
    args = parser.parse_args()

    extract_graph_edge_matrices(args.dataset_type)
