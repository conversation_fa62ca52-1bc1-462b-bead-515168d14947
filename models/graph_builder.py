import pandas as pd
import torch
from torch_geometric.data import HeteroData
from typing import Dict
import networkx as nx
from tqdm import tqdm
from itertools import combinations
from collections import defaultdict
import os
import pickle

class GraphBuilder:
    def __init__(self, train_data, val_data, test_data, save_dir, dataset_name):
        """
        Initialize GraphBuilder with train, validation and test datasets.
        We'll primarily use train_data for graph construction to prevent data leakage.
        """
        self.train_data = train_data
        self.val_data = val_data
        self.test_data = test_data
        self.save_dir = save_dir
        self.dataset_name = dataset_name
        os.makedirs(save_dir, exist_ok=True)

        # format item column
        if dataset_name == 'movielens':
            self.item_column = 'movie_id'
        elif dataset_name == 'bookcrossing':
            self.item_column = 'item_id'
        elif dataset_name == 'amazon':
            self.item_column = 'item_id'
        else:
            raise ValueError(f"Check Dataset {dataset_name} !!!")

        # Load mappings from processed directory (consistent with build script)
        if dataset_name == 'bookcrossing':
            mapping_dir = '/data/datasets/processed_datasets/bookcrossing/id_mappings'
            # Load user_id mapping
            user_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'user_id_mapping.csv'))
            self.user_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['index']))
            self.user_ids = user_mapping_df['user_id'].values
            # Load item_id mapping
            item_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'item_id_mapping.csv'))
            self.item_to_idx = dict(zip(item_mapping_df['item_id'], item_mapping_df['index']))
            self.item_ids = item_mapping_df['item_id'].values
        elif dataset_name == 'amazon':
            mapping_dir = '/data/datasets/processed_datasets/amazon/id_mappings'
            # Load user_id mapping (different file name for Amazon)
            user_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'user_id_to_embedding_index_map.csv'))
            self.user_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['embedding_idx']))
            self.user_ids = user_mapping_df['user_id'].values
            # Load item_id mapping (different file name for Amazon)
            item_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'item_id_to_embedding_index_map.csv'))
            self.item_to_idx = dict(zip(item_mapping_df['item_id'], item_mapping_df['embedding_idx']))
            self.item_ids = item_mapping_df['item_id'].values
        else:
            mapping_dir = '/data/datasets/movielens/id_mappings'  # Keep original for MovieLens
            # Load user_id mapping
            user_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'user_id_mapping.csv'))
            self.user_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['index']))
            self.user_ids = user_mapping_df['user_id'].values
            # Load item_id mapping
            item_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'item_id_mapping.csv'))
            self.item_to_idx = dict(zip(item_mapping_df['item_id'], item_mapping_df['index']))
            self.item_ids = item_mapping_df['item_id'].values

        self.save_mappings()
    def save_mappings(self):
        """Save the id mappings"""
        mappings = {
            'user_to_idx': self.user_to_idx,
            'item_to_idx': self.item_to_idx,
            'user_ids': self.user_ids,
            'item_ids': self.item_ids
        }
        with open(os.path.join(self.save_dir, 'id_mappings.pkl'), 'wb') as f:
            pickle.dump(mappings, f)
        print(f"Saved id mappings to {self.save_dir}")

    def save_graph(self, graph, graph_name):
        """Save a single graph"""
        file_path = os.path.join(self.save_dir, f'{graph_name}.pt')
        torch.save(graph, file_path)
        print(f"Saved {graph_name} to {file_path}")

    def build_and_save_all_graphs(self):
        """Build and save graphs one by one"""
        print("Building and saving user-item graph...")
        user_item_graph = self.build_user_item_graph()
        print('Starting to save user-item graph...')
        self.save_graph(user_item_graph, 'user_item_graph')
        del user_item_graph  # Free memory

        print("\nBuilding and saving user graph...")
        user_graph = self.build_user_graph()
        print('Starting to save user graph...')
        self.save_graph(user_graph, 'user_graph')
        del user_graph  # Free memory

        print("\nBuilding and saving item graph...")
        item_graph = self.build_item_graph()
        print('Starting to save item graph...')
        self.save_graph(item_graph, 'item_graph')
        del item_graph  # Free memory

    def build_user_item_graph(self):
        """
        Builds a user-item heterogeneous graph with two types of edges:
        - click: User clicked on the item (label=1)
        - impression: User was shown the item (all interactions)
        Only includes user->item direction.
        """
        data = HeteroData()

        # Add node types - no change needed here
        data['user'].num_nodes = len(self.user_ids)
        data['item'].num_nodes = len(self.item_ids)
        # Add edges - click and impression
        users = torch.tensor([self.user_to_idx[uid] for uid in self.train_data['user_id']])
        items = torch.tensor([self.item_to_idx[iid] for iid in self.train_data[self.item_column]])
        data['user', 'impression', 'item'].edge_index = torch.stack([users, items])
        click_mask = self.train_data['label'] == 1
        click_users = torch.tensor([self.user_to_idx[uid] for uid in self.train_data[click_mask]['user_id']])
        click_items = torch.tensor([self.item_to_idx[iid] for iid in self.train_data[click_mask][self.item_column]])
        data['user', 'click', 'item'].edge_index = torch.stack([click_users, click_items])

        return data

    def build_user_graph(self):
        """
        Builds a user-user undirected graph with two types of edges and their weights:
        - co_click: Users who clicked the same item (weight = normalized count of common clicks)
        - co_impression: Users who were shown the same item (weight = normalized count of common impressions)

        The weights are stored as edge attributes and can be ignored during Node2Vec and Contrastive Learning.
        """
        data = HeteroData()
        data['user'].num_nodes = len(self.user_ids)

        # For co-click edges and weights
        clicked_groups = self.train_data[self.train_data['label'] == 1].groupby(self.item_column)['user_id'].agg(list)
        click_edges = []
        click_weights = defaultdict(int)  # (user1, user2) -> count of common clicks

        for users in tqdm(clicked_groups):
            if len(users) > 1:
                users = [self.user_to_idx[u] for u in users]
                # Generate all pairs
                for u1, u2 in combinations(users, 2):
                    ordered_pair = (min(u1, u2), max(u1, u2))
                    click_edges.append(ordered_pair)
                    click_weights[ordered_pair] += 1

        # For co-impression edges and weights
        impressed_groups = self.train_data.groupby(self.item_column)['user_id'].agg(list)
        impression_edges = []
        impression_weights = defaultdict(int)  # (user1, user2) -> count of common impressions

        for users in tqdm(impressed_groups):
            if len(users) > 1:
                users = [self.user_to_idx[u] for u in users]
                # Generate all pairs
                for u1, u2 in combinations(users, 2):
                    ordered_pair = (min(u1, u2), max(u1, u2))
                    impression_edges.append(ordered_pair)
                    impression_weights[ordered_pair] += 1

        # Convert edges and weights to tensors and store in graph
        print('Creating click edges...')
        if click_edges:
            # Remove duplicates while preserving order
            click_edges = list(dict.fromkeys(click_edges))
            click_edges_tensor = torch.tensor(click_edges).t()
            data['user', 'co_click', 'user'].edge_index = click_edges_tensor

            # Create and normalize weights
            click_weights_tensor = torch.tensor([click_weights[(min(u1, u2), max(u1, u2))] for u1, u2 in click_edges], dtype=torch.float)
            min_weight = click_weights_tensor.min()
            max_weight = click_weights_tensor.max()
            click_weights_tensor = (click_weights_tensor - min_weight) / (max_weight - min_weight)

            # Store weights as edge attribute
            data['user', 'co_click', 'user'].edge_weight = click_weights_tensor
        else:
            data['user', 'co_click', 'user'].edge_index = torch.empty((2, 0), dtype=torch.long)
            data['user', 'co_click', 'user'].edge_weight = torch.empty(0, dtype=torch.float)

        print('Creating impression edges...')
        if impression_edges:
            # Remove duplicates while preserving order
            impression_edges = list(dict.fromkeys(impression_edges))
            impression_edges_tensor = torch.tensor(impression_edges).t()
            data['user', 'co_impression', 'user'].edge_index = impression_edges_tensor

            # Create and normalize weights
            impression_weights_tensor = torch.tensor([impression_weights[(min(u1, u2), max(u1, u2))] for u1, u2 in impression_edges], dtype=torch.float)
            min_weight = impression_weights_tensor.min()
            max_weight = impression_weights_tensor.max()
            impression_weights_tensor = (impression_weights_tensor - min_weight) / (max_weight - min_weight)

            # Store weights as edge attribute
            data['user', 'co_impression', 'user'].edge_weight = impression_weights_tensor
        else:
            data['user', 'co_impression', 'user'].edge_index = torch.empty((2, 0), dtype=torch.long)
            data['user', 'co_impression', 'user'].edge_weight = torch.empty(0, dtype=torch.float)

        return data

    def build_item_graph(self):
        """
        Builds an item-item undirected graph with two types of edges and their weights:
        - co_clicked: Items clicked by the same user (weight = normalized count of co-clicks)
        - co_impressed: Items shown to the same user (weight = normalized count of co-impressions)
        """
        data = HeteroData()
        data['item'].num_nodes = len(self.item_ids)

        # For co-clicked relationships
        clicked_groups = self.train_data[self.train_data['label'] == 1].groupby('user_id')[self.item_column].agg(list)
        clicked_edges = []
        clicked_weights = defaultdict(int)

        for items in tqdm(clicked_groups):
            if len(items) > 1:
                items = [self.item_to_idx[i] for i in items]
                # Generate all pairs
                for i1, i2 in combinations(items, 2):
                    ordered_pair = (min(i1, i2),max(i1, i2))
                    clicked_edges.append(ordered_pair)
                    clicked_weights[ordered_pair] += 1

        # For co-impressed relationships
        impressed_groups = self.train_data.groupby('user_id')[self.item_column].agg(list)
        impressed_edges = []
        impressed_weights = defaultdict(int)

        for items in tqdm(impressed_groups):
            if len(items) > 1:
                items = [self.item_to_idx[i] for i in items]
                # Generate all pairs
                for i1, i2 in combinations(items, 2):
                    ordered_pair = (min(i1, i2),max(i1, i2))
                    impressed_edges.append(ordered_pair)
                    impressed_weights[ordered_pair] += 1

        # Convert edges and weights to tensors
        if clicked_edges:
            # Remove duplicates while preserving order
            clicked_edges = list(dict.fromkeys(clicked_edges))
            clicked_edges_tensor = torch.tensor(clicked_edges).t()
            data['item', 'co_clicked', 'item'].edge_index = clicked_edges_tensor

            # Create and normalize weights
            clicked_weights_tensor = torch.tensor([
                clicked_weights[(min(i1, i2), max(i1, i2))]
                for i1, i2 in clicked_edges
            ], dtype=torch.float)
            min_weight = clicked_weights_tensor.min()
            max_weight = clicked_weights_tensor.max()
            print('click min weight', min_weight)
            print('click max weight', max_weight)
            clicked_weights_tensor = (clicked_weights_tensor - min_weight) / (max_weight - min_weight)

            # Store weights as edge attribute
            data['item', 'co_clicked', 'item'].edge_weight = clicked_weights_tensor
        else:
            data['item', 'co_clicked', 'item'].edge_index = torch.empty((2, 0), dtype=torch.long)
            data['item', 'co_clicked', 'item'].edge_weight = torch.empty(0, dtype=torch.float)

        if impressed_edges:
            # Remove duplicates while preserving order
            impressed_edges = list(dict.fromkeys(impressed_edges))
            impressed_edges_tensor = torch.tensor(impressed_edges).t()
            data['item', 'co_impressed', 'item'].edge_index = impressed_edges_tensor

            # Create and normalize weights
            impressed_weights_tensor = torch.tensor([
                impressed_weights[(min(i1, i2), max(i1, i2))]
                for i1, i2 in impressed_edges
            ], dtype=torch.float)
            min_weight = impressed_weights_tensor.min()
            max_weight = impressed_weights_tensor.max()
            print('impression min weight', min_weight)
            print('impression max weight', max_weight)
            impressed_weights_tensor = (impressed_weights_tensor - min_weight) / (max_weight - min_weight)

            # Store weights as edge attribute
            data['item', 'co_impressed', 'item'].edge_weight = impressed_weights_tensor
        else:
            data['item', 'co_impressed', 'item'].edge_index = torch.empty((2, 0), dtype=torch.long)
            data['item', 'co_impressed', 'item'].edge_weight = torch.empty(0, dtype=torch.float)

        return data

    def get_graph_stats(self, data):
        """
        Returns basic statistics about the heterogeneous graph
        """
        stats = {
            'num_users': data['user'].num_nodes,
            'num_items': data['item'].num_nodes,
            'num_click_edges': data['user', 'click', 'item'].edge_index.size(1),
            'num_impression_edges': data['user', 'impression', 'item'].edge_index.size(1),
        }

        # Add edge density
        total_possible = stats['num_users'] * stats['num_items']
        stats['click_density'] = stats['num_click_edges'] / total_possible
        stats['impression_density'] = stats['num_impression_edges'] / total_possible

        return stats

    def build_interaction_matrices(self):
        """
        Build interaction matrices for contrastive learning:
        - click_matrix: Binary matrix indicating if user clicked item
        - impression_matrix: Binary matrix indicating if user was shown item
        - user_edge_type1: Binary matrix for user-user co-click connections
        - user_edge_type2: Binary matrix for user-user co-impression connections
        - item_edge_type1: Binary matrix for item-item co-clicked connections
        - item_edge_type2: Binary matrix for item-item co-impressed connections

        Returns:
            Dict containing all matrices as sparse tensors
        """
        print('Start building interaction matrices')
        num_users = len(self.user_ids)
        num_items = len(self.item_ids)

        # Convert interactions to user and item indices
        user_indices = torch.tensor([self.user_to_idx[uid] for uid in self.train_data['user_id']])
        item_indices = torch.tensor([self.item_to_idx[iid] for iid in self.train_data[self.item_column]])
        click_mask = self.train_data['label'] == 1

        # Build click and impression matrices (user x item)
        click_matrix = torch.sparse_coo_tensor(
            indices=torch.stack([user_indices[click_mask],item_indices[click_mask]]),
            values=torch.ones(click_mask.sum(), dtype=torch.float),
            size=(num_users, num_items)
        )

        impression_matrix = torch.sparse_coo_tensor(
            indices=torch.stack([user_indices, item_indices]),
            values=torch.ones(len(user_indices), dtype=torch.float),
            size=(num_users, num_items)
        )

        # Build user-user matrices
        user_edge_type1 = self._build_cooccurrence_matrix(
            self.train_data[click_mask],
            self.item_column, 'user_id',
            num_nodes=num_users
        )

        user_edge_type2 = self._build_cooccurrence_matrix(
            self.train_data,
            self.item_column, 'user_id',
            num_nodes=num_users
        )

        # Build item-item matrices
        item_edge_type1 = self._build_cooccurrence_matrix(
            self.train_data[click_mask],
            'user_id', self.item_column,
            num_nodes=num_items
        )

        item_edge_type2 = self._build_cooccurrence_matrix(
            self.train_data,
            'user_id', self.item_column,
            num_nodes=num_items
        )

        # Save matrices
        print('Saving matrices')
        torch.save(click_matrix, os.path.join(self.save_dir, 'click_matrix.pt'))
        print('Save impression_matrix')
        torch.save(impression_matrix, os.path.join(self.save_dir, 'impression_matrix.pt'))
        print('Save user_edge_type1')
        torch.save(user_edge_type1, os.path.join(self.save_dir, 'user_edge_type1.pt'))
        print('Save user_edge_type2')
        torch.save(user_edge_type2, os.path.join(self.save_dir, 'user_edge_type2.pt'))
        print('Save item_edge_type1')
        torch.save(item_edge_type1, os.path.join(self.save_dir, 'item_edge_type1.pt'))
        print('Save item_edge_type2')
        torch.save(item_edge_type2, os.path.join(self.save_dir, 'item_edge_type2.pt'))

        print(f"Saved interaction matrices to {self.save_dir}")


    def _build_cooccurrence_matrix(self, data, group_col, target_col, num_nodes):
        """
        Build co-occurrence matrix from grouped interactions
        """
        # Get groups of entities that share the same interaction
        groups = data.groupby(group_col)[target_col].agg(list)

        # Generate co-occurrence pairs
        row_indices = []
        col_indices = []

        for group in groups:
            if len(group) > 1:
                # Convert ids to indices
                indices = [
                    self.user_to_idx[x] if target_col == 'user_id' else self.item_to_idx[x]
                    for x in group
                ]
                # Generate all pairs
                for i in range(len(indices)):
                    for j in range(i + 1, len(indices)):
                        row_indices.extend([indices[i], indices[j]])
                        col_indices.extend([indices[j], indices[i]])

        # Create sparse matrix
        if row_indices:  # Check if we have any edges
            indices = torch.stack([
                torch.tensor(row_indices),
                torch.tensor(col_indices)
            ])
            values = torch.ones(len(row_indices), dtype=torch.float)
            matrix = torch.sparse_coo_tensor(
                indices=indices,
                values=values,
                size=(num_nodes, num_nodes)
            )
        else:
            matrix = torch.sparse_coo_tensor(
                indices=torch.empty((2, 0), dtype=torch.long),
                values=torch.empty(0, dtype=torch.float),
                size=(num_nodes, num_nodes)
            )

        return matrix

if __name__ == "__main__":
    # dataset name
    dataset_name = 'amazon'

    if dataset_name == 'movielens':
        # Load your processed datasets
        train_data = pd.read_csv('/data/datasets/processed_datasets/movielens/train.csv')
        val_data = pd.read_csv('/data/datasets/processed_datasets/movielens/val.csv')
        test_data = pd.read_csv('/data/datasets/processed_datasets/movielens/test.csv')
        save_dir = '/data/datasets/processed_datasets/movielens/graph'
    elif dataset_name == 'bookcrossing':
        # Load your processed datasets
        train_data = pd.read_csv('/data/datasets/processed_datasets/bookcrossing/train.csv')
        val_data = pd.read_csv('/data/datasets/processed_datasets/bookcrossing/val.csv')
        test_data = pd.read_csv('/data/datasets/processed_datasets/bookcrossing/test.csv')
        save_dir = '/data/datasets/processed_datasets/bookcrossing/graph'
    elif dataset_name == 'amazon':
        # Load your processed datasets
        train_data = pd.read_csv('/data/datasets/processed_datasets/amazon/train.csv')
        val_data = pd.read_csv('/data/datasets/processed_datasets/amazon/val.csv')
        test_data = pd.read_csv('/data/datasets/processed_datasets/amazon/test.csv')
        save_dir = '/data/datasets/processed_datasets/amazon/graph'
    else:
        raise ValueError(f"Check Dataset {dataset_name} !!!")

    os.makedirs(save_dir, exist_ok=True)

    # Initialize GraphBuilder
    graph_builder = GraphBuilder(train_data, val_data, test_data, save_dir, dataset_name)

    #  Construct the graphs
    graph_builder.build_and_save_all_graphs()

    # Build interaction matrices
    graph_builder.build_interaction_matrices()

