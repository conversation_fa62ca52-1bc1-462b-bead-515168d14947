import os
import torch
import torch.nn as nn
from typing import List
from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from tqdm import tqdm
import pandas as pd
pd.set_option('display.max_colwidth', 999)

def format_genres(x):
    if len(x) == 1:
        return x[0]
    else:
        return ', '.join(x[:-1]) + ' and ' + x[-1]

def format_authors(x):
    if len(x) == 1:
        return x[0]
    else:
        return ', '.join(x[:-1]) + ' and ' + x[-1]

def template_process_movielens(x, type='user'):
    if type == 'user':
        template = f"This is a user, user ID is {x['user_id']}, gender is {x['gender_text']}, age range is {x['age_text']}, occupation is {x['occupation_text']}, zip code is {x['zip_code_text']}."
    elif type == 'item':
        template = f"This is a movie, movie ID is {x['movie_id']}, genres is {x['genres_text']}."
    else:
        raise ValueError("Invalid type")

    return template

def template_process_bookcrossing(x, type='user'):
    if type == 'user':
        age_text = "unknown" if x['age'] == -1 else str(x['age'])
        country_text = "unknown" if pd.isna(x['country']) or x['country'] == -1 else x['country']
        state_text = "unknown" if pd.isna(x['state']) or x['state'] == -1 else x['state']
        city_text = "unknown" if pd.isna(x['city']) or x['city'] == -1 else x['city']
        template = f"This is a user, user ID is {x['user_id']}, age is {age_text}, country is {country_text}, state is {state_text}, city is {city_text}."
    elif type == 'item':
        year_text = "unknown" if pd.isna(x['year']) or x['year'] == -1 else str(x['year'])
        publisher_text = "unknown" if pd.isna(x['publisher']) or x['publisher'] == -1 else x['publisher']
        authors_text = "unknown" if pd.isna(x['authors_text']) or x['authors_text'] == "" else x['authors_text']
        template = f"This is a book, book ID is {x['item_id']}, authors are {authors_text}, year of publication is {year_text}, publisher is {publisher_text}."
    else:
        raise ValueError("Invalid type")

    return template

def template_process_amazon(x, type='user'):
    if type == 'user':
        template = f"This is a user, user ID is {x['user_id']}."
    elif type == 'item':
        brand_text = "unknown" if pd.isna(x['brand']) or x['brand'] == "" else x['brand']
        categories_text = "unknown" if pd.isna(x['categories_text']) or x['categories_text'] == "" else x['categories_text']
        template = f"This is a product, product ID is {x['item_id']}, title is {x['title']}, brand is {brand_text}, categories are {categories_text}."
    else:
        raise ValueError("Invalid type")

    return template

class TextEncoder(nn.Module):
    def __init__(self, model_name, device):
        """
        Initialize text encoder using pretrained transformer model
        """
        super().__init__()
        self.device = device

        # Initialize tokenizer and model
        self.tokenizer = RobertaTokenizer.from_pretrained(model_name)
        self.model = RobertaModel.from_pretrained(model_name)

        # Freeze the model
        self.model.eval()
        for param in self.model.parameters():
            param.requires_grad = False

        # Move to device
        self.to(device)

    def forward(self, prompts):
        """
        Get text embeddings
        """
        # Tokenize prompts
        tokens = self.tokenizer(
            prompts,
            padding=True,
            truncation=True,
            max_length=128,
            return_tensors="pt"
        ).to(self.device)

        # Get embeddings
        with torch.no_grad():
            outputs = self.model(**tokens)
            # Use mean pooling to get sequence representation
            attention_mask = tokens['attention_mask'].unsqueeze(-1)
            embeddings = (outputs.last_hidden_state * attention_mask).sum(dim=1) / attention_mask.sum(dim=1)
        return embeddings

    def get_embeddings(self, data_dir, data_user, data_item, batch_size, dataset_type):
        """
        Get embeddings for all users and items
        """
        if dataset_type == 'movielens':
            item_column = 'movie_id'
        elif dataset_type == 'bookcrossing':
            item_column = 'item_id'
        elif dataset_type == 'amazon':
            item_column = 'item_id'
        else:
            raise ValueError(f"Unsupported dataset type: {dataset_type}")

        # Load mappings from processed directory (consistent with build script)
        if dataset_type == 'bookcrossing':
            mapping_dir = '/data/datasets/processed_datasets/bookcrossing/id_mappings'
        elif dataset_type == 'amazon':
            mapping_dir = '/data/datasets/processed_datasets/amazon'
        else:
            mapping_dir = '/data/datasets/movielens/id_mappings'  # Keep original for MovieLens

        # Load user_id mapping
        user_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'user_id_mapping.csv'))
        user_id_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['index']))

        # Load item_id mapping
        item_mapping_df = pd.read_csv(os.path.join(mapping_dir, 'item_id_mapping.csv'))
        item_id_to_idx = dict(zip(item_mapping_df[item_column], item_mapping_df['index']))

        max_user_idx = max(user_id_to_idx.values())
        max_item_idx = max(item_id_to_idx.values())
        # RoBERTa's embedding dimension
        embedding_dim = self.model.config.hidden_size

        user_embeddings = torch.zeros((max_user_idx + 1, embedding_dim), device=self.device)
        item_embeddings = torch.zeros((max_item_idx + 1, embedding_dim), device=self.device)

        # Get user embeddings
        print("Processing user text embeddings")
        data_user = data_user.reset_index(drop=True)
        for i in tqdm(range(0, len(data_user), batch_size)):
            batch = data_user.iloc[i:i+batch_size]
            prompts = batch['prompt'].tolist()
            embeddings = self.forward(prompts)
            for j, user_id in enumerate(batch['user_id']):
                idx = user_id_to_idx.get(user_id, 0)
                user_embeddings[idx] = embeddings[j]

        # Get item embeddings
        print("Processing item text embeddings")
        data_item = data_item.reset_index(drop=True)
        for i in tqdm(range(0, len(data_item), batch_size)):
            batch = data_item.iloc[i:i+batch_size]
            prompts = batch['prompt'].tolist()
            embeddings = self.forward(prompts)

            for j, x_id in enumerate(batch[item_column]):
                idx = item_id_to_idx.get(x_id, 0)
                item_embeddings[idx] = embeddings[j]

        return user_embeddings, item_embeddings

if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    dataset_type = 'amazon'

    if dataset_type == 'movielens':
        data_dir = '/data/datasets/processed_datasets/movielens/'
        save_dir = '/data/datasets/processed_datasets/movielens/text_embeddings'

        # Mapping
        age_mapping = pd.read_csv('/data/datasets/movielens/age_mapping.csv')
        occupation_mapping = pd.read_csv('/data/datasets/movielens/occupation_mapping.csv')
        zip_code_mapping = pd.read_csv('/data/datasets/movielens/zipcode_mapping.csv')

        # Load data
        train_data = pd.read_csv(f"{data_dir}/train.csv")
        val_data = pd.read_csv(f"{data_dir}/val.csv")
        test_data = pd.read_csv(f"{data_dir}/test.csv")

        data = pd.concat([train_data, val_data, test_data], ignore_index=True)

        # User
        data_user = data[['user_id','gender','age','occupation','zip_code_index']].drop_duplicates()
        data_user = data_user.sort_values('user_id').reset_index(drop=True)
        # Item
        data_item = data[['movie_id','genres']].drop_duplicates()
        data_item = data_item.sort_values('movie_id').reset_index(drop=True)

        # Convert index to text
        data_user['gender_text'] = data_user['gender'].map({1: 'male', 2: 'female'})
        data_user['age_text'] = data_user['age'].apply(lambda x: age_mapping[age_mapping['index'] == x]['age'].values[0])
        data_user['occupation_text'] = data_user['occupation'].apply(lambda x: occupation_mapping[occupation_mapping['index'] == x]['occupation'].values[0])
        data_user['zip_code_text'] = data_user['zip_code_index'].apply(lambda x: zip_code_mapping[zip_code_mapping['index'] == x]['zip_code_v2'].values[0])

        data_item['genres_list'] = data_item['genres'].apply(lambda x: x.split('|'))
        data_item['genres_text'] = data_item['genres_list'].apply(lambda x: format_genres(x))

        # Prompt template
        print("Generating prompts!")
        data_user['prompt'] = data_user.apply(lambda x: template_process_movielens(x, type='user'), axis=1)
        data_item['prompt'] = data_item.apply(lambda x: template_process_movielens(x, type='item'), axis=1)

    elif dataset_type == 'bookcrossing':
        data_dir = '/data/datasets/processed_datasets/bookcrossing/'
        save_dir = '/data/datasets/processed_datasets/bookcrossing/text_embeddings'

        # Load data
        train_data = pd.read_csv(f"{data_dir}/train.csv")
        val_data = pd.read_csv(f"{data_dir}/val.csv")
        test_data = pd.read_csv(f"{data_dir}/test.csv")

        data = pd.concat([train_data, val_data, test_data], ignore_index=True)

        # User - use the cleaned text columns, not the index columns
        data_user = data[['user_id','age','country','state','city']].drop_duplicates()
        data_user = data_user.sort_values('user_id').reset_index(drop=True)
        # Item - use the cleaned text columns, not the index columns
        data_item = data[['item_id','author','year','publisher']].drop_duplicates()
        data_item = data_item.sort_values('item_id').reset_index(drop=True)

        # Process authors
        data_item['authors_list'] = data_item['author'].apply(lambda x: x.split('|') if isinstance(x, str) and not pd.isna(x) else [])
        data_item['authors_text'] = data_item['authors_list'].apply(lambda x: format_authors(x) if len(x) > 0 else "unknown")

        # Prompt template
        print("Generating prompts!")
        data_user['prompt'] = data_user.apply(lambda x: template_process_bookcrossing(x, type='user'), axis=1)
        data_item['prompt'] = data_item.apply(lambda x: template_process_bookcrossing(x, type='item'), axis=1)

    elif dataset_type == 'amazon':
        data_dir = '/data/datasets/processed_datasets/amazon/'
        save_dir = '/data/datasets/processed_datasets/amazon/text_embeddings'

        # Load data
        train_data = pd.read_csv(f"{data_dir}/train.csv")
        val_data = pd.read_csv(f"{data_dir}/val.csv")
        test_data = pd.read_csv(f"{data_dir}/test.csv")

        data = pd.concat([train_data, val_data, test_data], ignore_index=True)

        # User - Amazon users only have user_id
        data_user = data[['user_id']].drop_duplicates()
        data_user = data_user.sort_values('user_id').reset_index(drop=True)

        # Item - Amazon items have title, brand, category
        data_item = data[['item_id', 'title', 'brand', 'category_list']].drop_duplicates()
        data_item = data_item.sort_values('item_id').reset_index(drop=True)

        # Process categories
        data_item['categories_text'] = data_item['category_list'].apply(
            lambda x: ', '.join(eval(x)) if isinstance(x, str) and x != '[]' else "unknown"
        )

        # Prompt template
        print("Generating prompts!")
        data_user['prompt'] = data_user.apply(lambda x: template_process_amazon(x, type='user'), axis=1)
        data_item['prompt'] = data_item.apply(lambda x: template_process_amazon(x, type='item'), axis=1)

    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}")

    print('total data size: ', data.shape)
    print('total user size: ', data_user.shape)
    print('total item size: ', data_item.shape)
    os.makedirs(save_dir, exist_ok=True)
    text_encoder_model = '/root/code/roberta-base'

    # Initialize text encoder and get embeddings
    print("Initializing text encoder")
    text_encoder = TextEncoder(model_name=text_encoder_model, device=device)

    # Get embeddings
    user_embeddings, item_embeddings = text_encoder.get_embeddings(data_dir, data_user, data_item, batch_size=16, dataset_type=dataset_type)

    # Save embeddings
    print("Saving embeddings")
    torch.save(user_embeddings, f"{save_dir}/user_text_embeddings.pt")
    torch.save(item_embeddings, f"{save_dir}/item_text_embeddings.pt")