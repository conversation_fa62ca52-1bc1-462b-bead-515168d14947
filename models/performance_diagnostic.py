# performance_diagnostic.py

import os
import time
import torch
import pandas as pd
import numpy as np
from tqdm import tqdm
import psutil
import GPUtil
from datetime import datetime
from torch.utils.data import DataLoader, Dataset

from contrastive_mod import ContrastiveLearner
from batch_builder import BatchBuilder

class DiagnosticDataset(Dataset):
    def __init__(self, data_path):
        self.data = pd.read_csv(data_path)
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        return {
            'user_id': row['user_id'],
            'item_id': row['movie_id'],
            'label': row['label']
        }

def get_system_info():
    """获取系统信息"""
    print("=== System Information ===")
    print(f"CPU cores: {psutil.cpu_count()}")
    print(f"Total RAM: {psutil.virtual_memory().total / (1024**3):.2f} GB")
    print(f"Available RAM: {psutil.virtual_memory().available / (1024**3):.2f} GB")
    
    if torch.cuda.is_available():
        print(f"\n=== GPU Information ===")
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.2f} GB")
        print(f"GPU Memory Allocated: {torch.cuda.memory_allocated(0) / (1024**3):.2f} GB")
        print(f"GPU Memory Cached: {torch.cuda.memory_reserved(0) / (1024**3):.2f} GB")
        
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                print(f"GPU {gpu.id} - Memory Free: {gpu.memoryFree}MB, Memory Used: {gpu.memoryUsed}MB")
                print(f"GPU {gpu.id} - GPU Utilization: {gpu.load*100}%")
        except:
            print("Unable to get detailed GPU stats")
    else:
        print("No GPU available")

def profile_data_loading(data_dir, batch_size=32, num_workers=4):
    """诊断数据加载性能"""
    print("\n=== Data Loading Profiling ===")
    
    dataset = DiagnosticDataset(os.path.join(data_dir, "train.csv"))
    print(f"Dataset size: {len(dataset)}")
    
    # 测试不同的worker数量
    for workers in [0, 2, 4, 8]:
        print(f"\nTesting with {workers} workers:")
        dataloader = DataLoader(
            dataset, 
            batch_size=batch_size, 
            shuffle=True, 
            num_workers=workers,
            pin_memory=True if torch.cuda.is_available() else False
        )
        
        start_time = time.time()
        for i, batch in enumerate(dataloader):
            if i >= 10:  # 只测试前10个batch
                break
        
        elapsed_time = time.time() - start_time
        print(f"Time for 10 batches: {elapsed_time:.2f} seconds")
        print(f"Average time per batch: {elapsed_time/10:.3f} seconds")

def profile_batch_building(data_dir, batch_size=32, device='cuda'):
    """诊断批次构建性能"""
    print("\n=== Batch Building Profiling ===")
    
    # 初始化batch builder
    batch_builder = BatchBuilder(data_dir, batch_size, device)
    
    # 创建测试数据
    dataset = DiagnosticDataset(os.path.join(data_dir, "train.csv"))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # 获取一个批次
    batch_data = next(iter(dataloader))
    user_ids = batch_data['user_id']
    item_ids = batch_data['item_id']
    labels = batch_data['label']
    
    # 详细计时每个步骤
    print(f"\nProfiling batch building with batch_size={batch_size}")
    
    total_start = time.time()
    
    # 1. ID映射
    start_time = time.time()
    user_indices, item_indices = batch_builder.map_ids_to_indices(user_ids, item_ids)
    print(f"ID mapping time: {time.time() - start_time:.3f} seconds")
    
    # 2. 特征嵌入
    start_time = time.time()
    user_feature_emb = batch_builder.user_feature_embeddings(user_indices)
    item_feature_emb = batch_builder.item_feature_embeddings(item_indices)
    print(f"Feature embedding time: {time.time() - start_time:.3f} seconds")
    
    # 3. 图嵌入索引
    start_time = time.time()
    graph_indices = {}
    for graph_type in ['user-item-user', 'user-item-item', 'user-user-click', 
                      'user-user-impression', 'item-item-click', 'item-item-impression']:
        graph_indices[graph_type] = batch_builder.map_graph_ids_to_indices(user_ids, item_ids, graph_type)
    print(f"Graph index mapping time: {time.time() - start_time:.3f} seconds")
    
    # 4. 矩阵提取
    start_time = time.time()
    click_matrix = batch_builder._extract_submatrix(batch_builder.click_matrix, user_ids, item_ids, 'user-item')
    impression_matrix = batch_builder._extract_submatrix(batch_builder.impression_matrix, user_ids, item_ids, 'user-item')
    print(f"User-item matrix extraction time: {time.time() - start_time:.3f} seconds")
    
    # 5. 大矩阵提取（这是主要瓶颈）
    start_time = time.time()
    user_edge_type1 = batch_builder._extract_and_sample_matrix(
        batch_builder.user_edge_type1, user_ids, user_ids, 'user-user', 
        sample_ratio=0.01, max_edges=5000
    )
    print(f"User edge type1 extraction time: {time.time() - start_time:.3f} seconds")
    print(f"  Extracted matrix: {user_edge_type1.shape}, nnz={user_edge_type1._nnz() if hasattr(user_edge_type1, '_nnz') else 'N/A'}")
    
    total_time = time.time() - total_start
    print(f"\nTotal batch building time: {total_time:.3f} seconds")
    
    # 内存使用情况
    if device == 'cuda':
        print(f"\nGPU memory after batch building: {torch.cuda.memory_allocated() / (1024**3):.2f} GB")

def profile_model_forward(data_dir, batch_size=32, device='cuda'):
    """诊断模型前向传播性能"""
    print("\n=== Model Forward Pass Profiling ===")
    
    # 初始化模型和batch builder
    model = ContrastiveLearner().to(device)
    batch_builder = BatchBuilder(data_dir, batch_size, device)
    
    # 获取一个批次
    dataset = DiagnosticDataset(os.path.join(data_dir, "train.csv"))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    batch_data = next(iter(dataloader))
    
    # 构建批次
    print("Building batch...")
    start_time = time.time()
    batch = batch_builder.build_batch(batch_data['user_id'], batch_data['item_id'], batch_data['label'])
    build_time = time.time() - start_time
    print(f"Batch build time: {build_time:.3f} seconds")
    
    # 模型前向传播
    print("\nProfiling model forward pass...")
    model.eval()
    with torch.no_grad():
        # 预热
        _ = model(batch)
        
        # 实际计时
        start_time = time.time()
        outputs = model(batch)
        if device == 'cuda':
            torch.cuda.synchronize()
        forward_time = time.time() - start_time
    
    print(f"Model forward time: {forward_time:.3f} seconds")
    
    # 分析各个loss的计算时间
    print("\nProfiling individual loss computations...")
    with torch.no_grad():
        # Loss 1
        start_time = time.time()
        loss1 = model.contrastive_loss.user_item_contrastive_loss(
            outputs['user_graph_proj'],
            outputs['item_graph_proj'],
            batch['click_matrix'],
            batch['impression_matrix']
        )
        print(f"Loss 1 (user-item) time: {time.time() - start_time:.3f} seconds")
        
        # Loss 2
        start_time = time.time()
        loss2 = model.contrastive_loss.structure_based_loss(
            outputs['user_click_graph_proj'],
            outputs['user_imp_graph_proj'],
            batch['user_edge_type1'],
            batch['user_edge_type2']
        )
        print(f"Loss 2 (user structure) time: {time.time() - start_time:.3f} seconds")

def profile_matrix_operations(data_dir, device='cuda'):
    """专门诊断矩阵操作性能"""
    print("\n=== Matrix Operations Profiling ===")
    
    batch_builder = BatchBuilder(data_dir, 32, device)
    
    # 分析各个矩阵的大小
    print("\nMatrix sizes:")
    matrices = {
        'click_matrix': batch_builder.click_matrix,
        'impression_matrix': batch_builder.impression_matrix,
        'user_edge_type1': batch_builder.user_edge_type1,
        'user_edge_type2': batch_builder.user_edge_type2,
        'item_edge_type1': batch_builder.item_edge_type1,
        'item_edge_type2': batch_builder.item_edge_type2
    }
    
    for name, matrix in matrices.items():
        if isinstance(matrix, torch.sparse.Tensor):
            print(f"{name}: sparse {matrix.shape}, nnz={matrix._nnz()}, device={matrix.device}")
        else:
            print(f"{name}: dense {matrix.shape}, device={matrix.device}")
    
    # 测试不同大小的批次提取
    test_sizes = [8, 16, 32, 64]
    for size in test_sizes:
        print(f"\nTesting extraction with batch_size={size}")
        
        # 创建测试ID
        user_ids = torch.randint(0, 6000, (size,), device=device)
        item_ids = torch.randint(0, 3500, (size,), device=device)
        
        # 测试最耗时的操作
        start_time = time.time()
        submatrix = batch_builder._extract_and_sample_matrix(
            batch_builder.user_edge_type1, user_ids, user_ids, 'user-user',
            sample_ratio=0.01, max_edges=5000
        )
        extraction_time = time.time() - start_time
        
        print(f"  Extraction time: {extraction_time:.3f} seconds")
        if isinstance(submatrix, torch.sparse.Tensor):
            print(f"  Result: sparse {submatrix.shape}, nnz={submatrix._nnz()}")

def run_full_diagnostic(data_dir, batch_size=32, device='cuda'):
    """运行完整诊断"""
    print(f"Starting full diagnostic at {datetime.now()}")
    print(f"Data directory: {data_dir}")
    print(f"Batch size: {batch_size}")
    print(f"Device: {device}")
    print("="*50)
    
    # 1. 系统信息
    get_system_info()
    
    # 2. 数据加载性能
    profile_data_loading(data_dir, batch_size)
    
    # 3. 批次构建性能
    profile_batch_building(data_dir, batch_size, device)
    
    # 4. 模型前向传播性能
    profile_model_forward(data_dir, batch_size, device)
    
    # 5. 矩阵操作性能
    profile_matrix_operations(data_dir, device)
    
    print("\n" + "="*50)
    print(f"Diagnostic completed at {datetime.now()}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Performance diagnostic for contrastive learning")
    parser.add_argument("--data_dir", type=str, required=True, help="Path to data directory")
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size for testing")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    
    args = parser.parse_args()
    
    run_full_diagnostic(args.data_dir, args.batch_size, args.device)