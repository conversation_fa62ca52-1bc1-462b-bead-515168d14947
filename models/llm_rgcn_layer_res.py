import torch
import torch.nn as nn
from torch_geometric.nn import MessagePassing
from llm_message_update import LLMMessageUpdater


class ResLLMRGCNLayer(MessagePassing):
    def __init__(self, input_dim, hidden_dim, num_relations, layer_idx, num_layers, dropout=0.1, device="cpu"):
        super().__init__(aggr="add")
        self.device = device
        self.layer_idx = layer_idx
        self.dropout = dropout
        self.relation_weights = nn.ModuleList([
            nn.Linear(input_dim, hidden_dim) for _ in range(num_relations)
        ])
        self.self_loop = nn.Linear(input_dim, hidden_dim)

        self.msg_updater = LLMMessageUpdater(
            llm_model_name="roberta-base",
            gnn_num_layers=num_layers,
            llm_num_layers=12,
            layer_idx=layer_idx,
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            device=device
        )

        # Add layer normalization
        self.layer_norm = nn.LayerNorm(hidden_dim)

        # Add dropout
        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, x, edge_index, edge_type):
        """
        x: [num_nodes, input_dim]
        edge_index: [2, num_edges]
        edge_type: [num_edges] with values in [0, num_relations)
        """
        # Store original input for residual connection
        original_x = x

        # Ensure input tensors are on the correct device
        x = x.to(self.device)
        edge_index = edge_index.to(self.device)
        edge_type = edge_type.to(self.device)

        # Get message passing result
        message_output = self.propagate(edge_index, x=x, edge_type=edge_type)

        # Add self-loop
        self_loop_output = self.self_loop(x)

        # Combine message passing and self-loop
        combined_output = message_output + self_loop_output

        # Add residual connection if not the first layer
        if self.layer_idx > 0:
            # If dimensions don't match, project original_x
            if original_x.shape[-1] != combined_output.shape[-1]:
                if not hasattr(self, 'residual_proj'):
                    self.residual_proj = nn.Linear(original_x.shape[-1], combined_output.shape[-1]).to(self.device)
                residual = self.residual_proj(original_x)
            else:
                residual = original_x

            # Add residual connection
            combined_output = combined_output + residual

        # Apply layer normalization
        output = self.layer_norm(combined_output)

        # Apply dropout
        output = self.dropout_layer(output)

        return output

    def message(self, x_j, edge_type):
        """
        x_j: [num_edges, input_dim] (source node features)
        edge_type: [num_edges]
        """
        # Ensure input tensors are on the correct device
        x_j = x_j.to(self.device)
        edge_type = edge_type.to(self.device)

        # Create a new tensor with the correct hidden dimension size
        # This ensures we're working with the right dimensions from the start
        msg = torch.zeros((x_j.size(0), self.self_loop.out_features), device=self.device)

        for r_id in torch.unique(edge_type):
            mask = (edge_type == r_id)
            # Apply the relation-specific transformation
            transformed = self.relation_weights[r_id](x_j[mask])
            msg[mask] = transformed

        # Apply message updater
        msg = self.msg_updater(msg)

        # Apply dropout to messages
        msg = nn.functional.dropout(msg, p=self.dropout, training=self.training)

        return msg

    def update(self, aggr_out):
        # Ensure output tensor is on the correct device
        return aggr_out.to(self.device)


class ResLLMRGCNEncoder(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_relations, num_layers, dropout=0.1, device="cpu"):
        super().__init__()
        self.device = device
        self.hidden_dim = hidden_dim
        self.layers = nn.ModuleList()

        for l in range(num_layers):
            self.layers.append(ResLLMRGCNLayer(
                input_dim if l == 0 else hidden_dim,
                hidden_dim,
                num_relations,
                layer_idx=l,
                num_layers=num_layers,
                dropout=dropout,
                device=device
            ))

        # Final layer normalization
        self.final_norm = nn.LayerNorm(hidden_dim)

        # For deep residual connection
        if input_dim != hidden_dim:
            self.input_proj = nn.Linear(input_dim, hidden_dim)
        else:
            self.input_proj = nn.Identity()

    def forward(self, x, edge_index, edge_type):
        # Ensure input tensors are on the correct device
        x = x.to(self.device)
        edge_index = edge_index.to(self.device)
        edge_type = edge_type.to(self.device)

        # Store original input for deep residual connection
        original_x = self.input_proj(x)

        # Process through each layer
        layer_output = x
        for layer in self.layers:
            layer_output = layer(layer_output, edge_index, edge_type)

        # Add deep residual connection
        layer_output = layer_output + original_x

        # Apply final layer normalization
        layer_output = self.final_norm(layer_output)

        return layer_output
