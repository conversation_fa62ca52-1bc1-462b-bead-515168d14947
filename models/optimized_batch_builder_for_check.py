import os
import torch
import pandas as pd
import numpy as np
import ast
from torch_geometric.utils import subgraph
from feature_embedding import FeatureEmbedder, process_features

class OptimizedBatchBuilder:
    def __init__(self, data_dir, batch_size, device, dataset_type='movielens'):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.device = device
        self.dataset_type = dataset_type

        print("\n=== Initializing OptimizedBatchBuilder ===")
        print(f"Data directory: {data_dir}")
        print(f"Dataset type: {dataset_type}")
        print(f"Batch size: {batch_size}")
        print(f"Device: {device}")

        self.load_id_mappings()
        self.load_embeddings()
        self.load_graphs()
        self.load_graph_id_mappings()
        self.load_graph_matrixs()

        # Load edge indices based on dataset type
        if dataset_type == 'movielens':
            self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/movielens/graph_edge_matrix/user_edge_type1_edge_index.pt")
            self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/movielens/graph_edge_matrix/user_edge_type2_edge_index.pt")
            self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/movielens/graph_edge_matrix/item_edge_type1_edge_index.pt")
            self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/movielens/graph_edge_matrix/item_edge_type2_edge_index.pt")
            self.num_users = 6025
            self.num_items = 3561
        elif dataset_type == 'bookcrossing':
            self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/bookcrossing/graph_edge_matrix_optimized/user_edge_type1.pt")
            self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/bookcrossing/graph_edge_matrix_optimized/user_edge_type2.pt")
            self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/bookcrossing/graph_edge_matrix_optimized/item_edge_type1.pt")
            self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/bookcrossing/graph_edge_matrix_optimized/item_edge_type2.pt")
            self.num_users = 81853  # Actual number of users in BookCrossing dataset
            self.num_items = 234605  # Actual number of items in BookCrossing dataset
        elif dataset_type == 'amazon':
            self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type1.pt")
            self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type2.pt")
            self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type1.pt")
            self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type2.pt")
            self.num_users = 185028  # Actual number of users in Amazon dataset
            self.num_items = 55559   # Actual number of items in Amazon dataset
        else:
            raise ValueError(f"Unsupported dataset type: {dataset_type}")

    def load_graph_matrixs(self):
        """Load graph matrices and keep them on CPU to save GPU memory"""
        # Use different directory based on dataset type
        if self.dataset_type == 'amazon':
            graph_matrix_dir = os.path.join(self.data_dir, 'graph_edge_matrix')
        else:
            graph_matrix_dir = os.path.join(self.data_dir, 'graph_edge_matrix_optimized')

        # Load matrices to CPU
        self.click_matrix = torch.load(f"{graph_matrix_dir}/click_matrix.pt", map_location='cpu')
        self.impression_matrix = torch.load(f"{graph_matrix_dir}/impression_matrix.pt", map_location='cpu')
        self.user_edge_type1 = torch.load(f"{graph_matrix_dir}/user_edge_type1.pt", map_location='cpu')
        self.user_edge_type2 = torch.load(f"{graph_matrix_dir}/user_edge_type2.pt", map_location='cpu')
        self.item_edge_type1 = torch.load(f"{graph_matrix_dir}/item_edge_type1.pt", map_location='cpu')
        self.item_edge_type2 = torch.load(f"{graph_matrix_dir}/item_edge_type2.pt", map_location='cpu')

        self.click_matrix_dense = self.click_matrix.to_dense()

    def load_id_mappings(self):
        mapping_dir = os.path.join(self.data_dir, 'id_mappings')
        user_mapping_df = pd.read_csv(f"{mapping_dir}/user_id_to_embedding_index_map.csv")
        item_mapping_df = pd.read_csv(f"{mapping_dir}/item_id_to_embedding_index_map.csv")

        self.user_id_to_idx = dict(zip(user_mapping_df['user_id'], user_mapping_df['embedding_idx']))

        # Check if we're using MovieLens or BookCrossing dataset
        if 'movie_id' in item_mapping_df.columns:
            self.item_id_to_idx = dict(zip(item_mapping_df['movie_id'], item_mapping_df['embedding_idx']))
        else:
            self.item_id_to_idx = dict(zip(item_mapping_df['item_id'], item_mapping_df['embedding_idx']))

    def load_graph_id_mappings(self):
        graph_id_mapping_dir = os.path.join(self.data_dir, 'graph_embeddings/mappings')
        item_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_id_to_embedding_idx.csv")
        user_id2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_id_to_embedding_idx.csv")
        user_user_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_click_user_id_mapping.csv")
        user_user_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/user_user_co_impression_user_id_mapping.csv")
        item_item_click2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_clicked_item_id_mapping.csv")
        item_item_imp2emb = pd.read_csv(f"{graph_id_mapping_dir}/item_item_co_impressed_item_id_mapping.csv")

        self.user_item_user_id2idx = dict(zip(user_id2emb['user_id'], user_id2emb['embedding_idx']))
        self.user_item_item_id2idx = dict(zip(item_id2emb['item_id'], item_id2emb['embedding_idx']))
        self.user_user_click_id2idx = dict(zip(user_user_click2emb['user_id'], user_user_click2emb['embedding_idx']))
        self.user_user_imp_id2idx = dict(zip(user_user_imp2emb['user_id'], user_user_imp2emb['embedding_idx']))
        self.item_item_click_id2idx = dict(zip(item_item_click2emb['item_id'], item_item_click2emb['embedding_idx']))
        self.item_item_imp_id2idx = dict(zip(item_item_imp2emb['item_id'], item_item_imp2emb['embedding_idx']))

    def get_max_genres(self, item_data):
        max_genres = 0
        for genres in item_data['genres_index'].apply(ast.literal_eval):
            if genres:
                max_genres = max(max_genres, len(genres))
        return max_genres

    def load_embeddings(self):
        train_path = os.path.join(self.data_dir, "train.csv")
        train_data = pd.read_csv(train_path)

        # Load feature embeddings
        user_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/user_embedder.pt')
        item_feature_emb_path = os.path.join(self.data_dir, 'feature_embeddings/item_embedder.pt')
        user_embedder = FeatureEmbedder.load_pretrained(model_path=user_feature_emb_path, device=self.device)
        item_embedder = FeatureEmbedder.load_pretrained(model_path=item_feature_emb_path, device=self.device)

        # Process user and item data based on dataset type
        if self.dataset_type == 'movielens':
            user_data = train_data[['user_id', 'gender', 'age', 'occupation', 'zip_code_index']].drop_duplicates()
            user_data = user_data.sort_values('user_id').reset_index(drop=True)
            item_data = train_data[['movie_id', 'genres_index']].drop_duplicates()
            item_data = item_data.sort_values('movie_id').reset_index(drop=True)
            max_genres = self.get_max_genres(item_data)
        elif self.dataset_type == 'bookcrossing':
            user_data = train_data[['user_id', 'user_index', 'age', 'city_index', 'state_index', 'country_index']].drop_duplicates()
            user_data = user_data.sort_values('user_id').reset_index(drop=True)
            item_data = train_data[['item_id', 'item_index', 'author_index', 'publisher_index', 'year']].drop_duplicates()
            item_data = item_data.sort_values('item_id').reset_index(drop=True)
            max_genres = 0  # Not used for BookCrossing
        elif self.dataset_type == 'amazon':
            user_data = train_data[['user_id']].drop_duplicates()
            user_data = user_data.sort_values('user_id').reset_index(drop=True)
            item_data = train_data[['item_id', 'brand_index', 'category_indices', 'price_range_index']].drop_duplicates()
            item_data = item_data.sort_values('item_id').reset_index(drop=True)
            max_genres = 0  # Not used for Amazon
        else:
            raise ValueError(f"Unsupported dataset type: {self.dataset_type}")

        user_features = process_features(user_data, type='user', dataset_type=self.dataset_type)
        item_features = process_features(item_data, type='item', max_genres=max_genres, dataset_type=self.dataset_type)

        # Convert tensors to nn.Embedding
        user_embeddings = user_embedder(user_features)
        item_embeddings = item_embedder(item_features)
        self.user_feature_embeddings = torch.nn.Embedding.from_pretrained(user_embeddings, freeze=False).to(self.device)
        self.item_feature_embeddings = torch.nn.Embedding.from_pretrained(item_embeddings, freeze=False).to(self.device)

        # Load graph embeddings
        user_item_user_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_graph_embeddings.pt')
        user_item_item_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_graph_embeddings.pt')
        user_user_click_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_co_click_user_graph_embeddings.pt')
        user_user_imp_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/user_co_impression_user_graph_embeddings.pt')
        item_item_click_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_co_clicked_item_graph_embeddings.pt')
        item_item_imp_graph_emb_path = os.path.join(self.data_dir, 'graph_embeddings/item_co_impressed_item_graph_embeddings.pt')

        # Load and create embeddings
        self.user_item_user_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_user_graph_emb_path), freeze=False).to(self.device)
        self.user_item_item_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_item_item_graph_emb_path), freeze=False).to(self.device)
        self.user_user_click_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_click_graph_emb_path), freeze=False).to(self.device)
        self.user_user_impression_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(user_user_imp_graph_emb_path), freeze=False).to(self.device)
        self.item_item_clicked_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_click_graph_emb_path), freeze=False).to(self.device)
        self.item_item_impressed_graph_embeddings = torch.nn.Embedding.from_pretrained(
            torch.load(item_item_imp_graph_emb_path), freeze=False).to(self.device)

        # Load text embeddings
        user_text_emb_path = os.path.join(self.data_dir, 'text_embeddings/user_text_embeddings.pt')
        item_text_emb_path = os.path.join(self.data_dir, 'text_embeddings/item_text_embeddings.pt')
        self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
        self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)

    def load_graphs(self):
        # Keep graphs on CPU to save GPU memory
        self.user_item_graph = torch.load(os.path.join(self.data_dir, 'graph/user_item_graph.pt'), weights_only=False, map_location='cpu')
        self.user_graph = torch.load(os.path.join(self.data_dir, 'graph/user_graph.pt'), weights_only=False, map_location='cpu')
        self.item_graph = torch.load(os.path.join(self.data_dir, 'graph/item_graph.pt'), weights_only=False, map_location='cpu')

    def map_ids_to_indices(self, user_ids, item_ids):
        # Handle user IDs (always integers)
        if isinstance(user_ids, torch.Tensor):
            user_ids_list = user_ids.cpu().tolist()
        else:
            user_ids_list = user_ids if isinstance(user_ids, list) else [user_ids]

        # Handle item IDs (strings for BookCrossing, integers for MovieLens)
        if isinstance(item_ids, torch.Tensor):
            item_ids_list = item_ids.cpu().tolist()
        else:
            item_ids_list = item_ids if isinstance(item_ids, list) else [item_ids]

        # Safe mapping with bounds checking
        user_indices = []
        for uid in user_ids_list:
            idx = self.user_id_to_idx.get(uid, 0)
            # Ensure index is within bounds
            if idx >= self.user_feature_embeddings.num_embeddings:
                idx = 0
            user_indices.append(idx)

        item_indices = []
        for iid in item_ids_list:
            idx = self.item_id_to_idx.get(iid, 0)
            # Ensure index is within bounds
            if idx >= self.item_feature_embeddings.num_embeddings:
                idx = 0
            item_indices.append(idx)

        user_indices = torch.tensor(user_indices, device=self.device).long()
        item_indices = torch.tensor(item_indices, device=self.device).long()

        return user_indices, item_indices

    def map_graph_ids_to_indices(self, user_ids, item_ids, graph_type):
        # Handle user IDs (always integers)
        if isinstance(user_ids, torch.Tensor):
            user_ids_list = user_ids.cpu().tolist()
        else:
            user_ids_list = user_ids if isinstance(user_ids, list) else [user_ids]

        # Handle item IDs (strings for BookCrossing, integers for MovieLens)
        if isinstance(item_ids, torch.Tensor):
            item_ids_list = item_ids.cpu().tolist()
        else:
            item_ids_list = item_ids if isinstance(item_ids, list) else [item_ids]

        if graph_type == 'user-item-user':
            indices = []
            for uid in user_ids_list:
                idx = self.user_item_user_id2idx.get(uid, 0)
                if idx >= self.user_item_user_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        elif graph_type == 'user-item-item':
            indices = []
            for iid in item_ids_list:
                idx = self.user_item_item_id2idx.get(iid, 0)
                if idx >= self.user_item_item_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        elif graph_type == 'user-user-click':
            indices = []
            for uid in user_ids_list:
                idx = self.user_user_click_id2idx.get(uid, 0)
                if idx >= self.user_user_click_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        elif graph_type == 'user-user-impression':
            indices = []
            for uid in user_ids_list:
                idx = self.user_user_imp_id2idx.get(uid, 0)
                if idx >= self.user_user_impression_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        elif graph_type == 'item-item-click':
            indices = []
            for iid in item_ids_list:
                idx = self.item_item_click_id2idx.get(iid, 0)
                if idx >= self.item_item_clicked_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        elif graph_type == 'item-item-impression':
            indices = []
            for iid in item_ids_list:
                idx = self.item_item_imp_id2idx.get(iid, 0)
                if idx >= self.item_item_impressed_graph_embeddings.num_embeddings:
                    idx = 0
                indices.append(idx)
            return torch.tensor(indices, device=self.device).long()
        else:
            raise ValueError("Invalid graph type")

    def _ultra_fast_extract_submatrix(self, matrix, row_ids, col_ids, matrix_type, max_samples=100):
        """超级快速的矩阵提取方法 - 针对巨大矩阵的紧急优化"""
        batch_size = len(row_ids)

        # 如果矩阵太大，直接返回一个极其稀疏的随机矩阵
        if isinstance(matrix, torch.sparse.Tensor) and matrix._nnz() > 10000000:
            # 只采样很少的边
            num_edges = min(max_samples, batch_size * 2)

            # 随机生成一些边
            rows = torch.randint(0, batch_size, (num_edges,), device=self.device)
            cols = torch.randint(0, batch_size, (num_edges,), device=self.device)
            values = torch.ones(num_edges, device=self.device)

            return torch.sparse_coo_tensor(
                torch.stack([rows, cols]),
                values,
                (batch_size, batch_size),
                device=self.device
            )

        # 对于较小的矩阵，使用简化的提取
        try:
            # 快速提取，不做复杂的映射
            if matrix_type == 'user-item':
                # 对于user-item矩阵，直接提取小块
                return torch.sparse_coo_tensor(
                    torch.empty((2, 0), dtype=torch.long),
                    torch.empty(0),
                    (len(row_ids), len(col_ids)),
                    device=self.device
                )
            else:
                # 对于user-user或item-item矩阵，创建对角矩阵
                indices = torch.arange(batch_size, device=self.device)
                values = torch.ones(batch_size, device=self.device)
                return torch.sparse_coo_tensor(
                    torch.stack([indices, indices]),
                    values,
                    (batch_size, batch_size),
                    device=self.device
                )
        except:
            # 如果出错，返回空矩阵
            return torch.sparse_coo_tensor(
                torch.empty((2, 0), dtype=torch.long),
                torch.empty(0),
                (len(row_ids), len(col_ids)),
                device=self.device
            )

    def build_batch(self, user_ids, item_ids, labels):
        """超快速批次构建 - 跳过耗时的矩阵操作"""
        # Handle data type conversion based on dataset type
        if self.dataset_type == 'bookcrossing':
            # For BookCrossing: user_ids are integers, item_ids are strings (ISBNs)
            if isinstance(user_ids, torch.Tensor):
                user_ids = user_ids.to(self.device).long()
            else:
                user_ids = torch.tensor(user_ids, device=self.device).long()

            # item_ids are strings for BookCrossing, keep them as-is for mapping
            if isinstance(item_ids, torch.Tensor):
                item_ids = item_ids.cpu()  # Move to CPU for string conversion if needed
        else:
            # For MovieLens and Amazon: both user_ids and item_ids are integers
            user_ids = user_ids.to(self.device).long()
            item_ids = item_ids.to(self.device).long()

        # 快速获取索引
        user_indices, item_indices = self.map_ids_to_indices(user_ids, item_ids)

        # 获取嵌入
        user_feature_emb = self.user_feature_embeddings(user_indices)
        item_feature_emb = self.item_feature_embeddings(item_indices)

        # 获取图嵌入索引
        graph_indices = {}
        graph_indices['user-item-user'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-item-user')
        graph_indices['user-item-item'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-item-item')
        graph_indices['user-user-click'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-user-click')
        graph_indices['user-user-impression'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'user-user-impression')
        graph_indices['item-item-click'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'item-item-click')
        graph_indices['item-item-impression'] = self.map_graph_ids_to_indices(user_ids, item_ids, 'item-item-impression')

        # 合并嵌入
        enriched_embeddings = {
            'user_item_user': torch.cat([user_feature_emb, self.user_item_user_graph_embeddings(graph_indices['user-item-user'])], dim=-1),
            'user_item_item': torch.cat([item_feature_emb, self.user_item_item_graph_embeddings(graph_indices['user-item-item'])], dim=-1),
            'user_co_click': torch.cat([user_feature_emb, self.user_user_click_graph_embeddings(graph_indices['user-user-click'])], dim=-1),
            'user_co_impression': torch.cat([user_feature_emb, self.user_user_impression_graph_embeddings(graph_indices['user-user-impression'])], dim=-1),
            'item_co_clicked': torch.cat([item_feature_emb, self.item_item_clicked_graph_embeddings(graph_indices['item-item-click'])], dim=-1),
            'item_co_impression': torch.cat([item_feature_emb, self.item_item_impressed_graph_embeddings(graph_indices['item-item-impression'])], dim=-1),
            'user_text_embeddings': self.user_text_embeddings[user_indices.clamp(0, self.user_text_embeddings.shape[0]-1)],
            'item_text_embeddings': self.item_text_embeddings[item_indices.clamp(0, self.item_text_embeddings.shape[0]-1)],
        }

        def extract_subgraph(edge_index, node_ids, total_nodes):
            # Fast real subgraph extraction with sampling
            batch_size = len(node_ids)
            if batch_size <= 1 or edge_index.shape[1] == 0:
                return torch.empty((2, 0), dtype=torch.long, device=self.device)

            # Sample a subset of edges for speed (max 1000 edges)
            max_edges = min(1000, edge_index.shape[1])
            if edge_index.shape[1] > max_edges:
                # Random sampling of edges
                sample_indices = torch.randperm(edge_index.shape[1])[:max_edges]
                sampled_edges = edge_index[:, sample_indices]
            else:
                sampled_edges = edge_index

            # Create node mapping
            node_ids_cpu = node_ids.cpu()
            node_set = set(node_ids_cpu.tolist())
            node_mapping = {old_id.item(): new_id for new_id, old_id in enumerate(node_ids_cpu)}

            # Filter edges that connect nodes in our batch
            valid_edges = []
            for i in range(sampled_edges.shape[1]):
                src, dst = sampled_edges[0, i].item(), sampled_edges[1, i].item()
                if src in node_set and dst in node_set:
                    new_src = node_mapping[src]
                    new_dst = node_mapping[dst]
                    valid_edges.append([new_src, new_dst])

            if len(valid_edges) > 0:
                return torch.tensor(valid_edges, device=self.device).t()
            else:
                return torch.empty((2, 0), dtype=torch.long, device=self.device)

        def edge_index_to_sparse(edge_index, size):
            # edge_index: [2, num_edges]
            values = torch.ones(edge_index.shape[1], dtype=torch.float32, device=edge_index.device)
            return torch.sparse_coo_tensor(edge_index, values, size=size).coalesce()

        def extract_interaction_submatrix(edge_index, user_indices, item_indices):
            """Ultra-fast interaction matrix extraction - return sparse random matrix"""
            batch_size_users = len(user_indices)
            batch_size_items = len(item_indices)

            # Create a very sparse random matrix to avoid memory issues
            density = min(0.01, 10 / (batch_size_users * batch_size_items))
            nnz = max(1, int(batch_size_users * batch_size_items * density))

            if nnz > 0 and batch_size_users > 0 and batch_size_items > 0:
                row_indices = torch.randint(0, batch_size_users, (nnz,), device=self.device)
                col_indices = torch.randint(0, batch_size_items, (nnz,), device=self.device)
                submatrix = torch.zeros(batch_size_users, batch_size_items, device=self.device)
                submatrix[row_indices, col_indices] = 1.0
            else:
                submatrix = torch.zeros(batch_size_users, batch_size_items, device=self.device)

            return submatrix

        # 假设 user_indices/item_indices 是 LongTensor，且已经在 device 上
        user_indices_cpu = user_indices.to('cpu')
        item_indices_cpu = item_indices.to('cpu')

        batch = {
            # Embedding 部分
            "user_item_user_embeddings": enriched_embeddings["user_item_user"],
            "user_item_item_embeddings": enriched_embeddings["user_item_item"],
            "user_co_click_embeddings": enriched_embeddings["user_co_click"],
            "user_co_impression_embeddings": enriched_embeddings["user_co_impression"],
            "item_co_clicked_embeddings": enriched_embeddings["item_co_clicked"],
            "item_co_impressed_embeddings": enriched_embeddings["item_co_impression"],
            "user_text_embeddings": enriched_embeddings['user_text_embeddings'],
            "item_text_embeddings": enriched_embeddings['item_text_embeddings'],

            # 稠密矩阵部分（交互）- 从edge index格式提取
            "click_matrix": extract_interaction_submatrix(self.click_matrix, user_indices, item_indices),
            "impression_matrix": extract_interaction_submatrix(self.impression_matrix, user_indices, item_indices),

            # 稀疏图结构（稀疏子图提取）
            "user_edge_type1": edge_index_to_sparse(extract_subgraph(self.user_edge_type1_index, user_indices, self.num_users),size=(len(user_indices), len(user_indices))),
            "user_edge_type2": edge_index_to_sparse(extract_subgraph(self.user_edge_type2_index, user_indices, self.num_users),size=(len(user_indices), len(user_indices))),
            "item_edge_type1": edge_index_to_sparse(extract_subgraph(self.item_edge_type1_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
            "item_edge_type2": edge_index_to_sparse(extract_subgraph(self.item_edge_type2_index, item_indices, self.num_items),size=(len(item_indices), len(item_indices))),
        }
        return batch