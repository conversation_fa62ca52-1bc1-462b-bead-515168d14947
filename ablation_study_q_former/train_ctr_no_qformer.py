"""
Training script for CTR prediction model without text-q-former in message passing.
Used for ablation studies to evaluate the contribution of text-q-former in message passing.
"""

import os
import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, get_linear_schedule_with_warmup
from tqdm import tqdm
import wandb
import random
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score

# Import from ablation study directory
from ablation_study_q_former.models.embedding_loader import EmbeddingLoader
from models.expert_fusion_focused import FocusedHybridExpertAdaptor
from models.qformer import TextQFormer
# Use the fixed graph input builder for ablation study
from ablation_study_q_former.models.graph_input_builder import build_rgcn_input, build_hgt_input
from ablation_study_q_former.models.llm_hgt_encoder_no_qformer import ResLLMHGTEncoderNoQFormer
from ablation_study_q_former.models.llm_rgcn_encoder_no_qformer import ResLLMRGCNEncoderNoQFormer


class CTRDataset(Dataset):
    def __init__(self, df):
        self.user_ids = df["user_id"].tolist()
        self.item_ids = df["movie_id"].tolist()
        self.labels = df["label"].tolist()

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        return {
            "user_id": self.user_ids[idx],
            "item_id": self.item_ids[idx],
            "label": self.labels[idx]
        }


def evaluate(model, dataloader, loader, hgt, rgcn, device):
    model.eval()
    all_probs, all_labels = [], []
    with torch.no_grad():
        for batch in dataloader:
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            user_feat = hgt_out[:len(user_ids)] + rgcn_out[:len(user_ids)]
            item_feat = hgt_out[len(user_ids):] + rgcn_out[len(user_ids):]

            prob, _, _ = model(user_feat, item_feat)
            all_probs.append(prob.squeeze().cpu())
            all_labels.append(labels.cpu())

    probs = torch.cat(all_probs).numpy()
    labels = torch.cat(all_labels).numpy()

    # Convert probabilities to binary predictions for accuracy calculation
    preds = (probs >= 0.5).astype(int)

    return {
        "auc": roc_auc_score(labels, probs),
        "logloss": log_loss(labels, probs),
        "accuracy": accuracy_score(labels, preds)
    }


def set_seed(seed):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)


def train_ctr_model_no_qformer(
    train_path,
    val_path,
    test_path,
    embedding_dir,
    item_meta_path,
    text_qformer,
    tokenizer,
    qformer_ckpt,
    device="cuda",
    epochs=10,
    batch_size=256,
    lr=5e-4,
    weight_decay=1e-5,
    early_stop_patience=5,
    warmup_steps=500,
    max_grad_norm=1.0,
    seed=42,
    log_dir="./ctr_logs_no_qformer",
    wandb_name="ctr_ablation_no_qformer",
    use_wandb=True,
    embedding_size=512  # 默认值设为512
):
    """
    Train CTR model without text-q-former in message passing.
    Note: We still use text-q-former for item feature extraction, but not in message passing.
    """
    print(f"\n{'='*50}")
    print(f"Starting CTR ablation study without text-q-former in message passing")
    print(f"embedding_size: {embedding_size}")
    print(f"{'='*50}\n")

    # Set random seed for reproducibility
    set_seed(seed)

    if use_wandb:
        wandb.init(project="GraphLLM4CTR_Ablation_Study", name=wandb_name)
        # Log hyperparameters
        wandb.config.update({
            "epochs": epochs,
            "batch_size": batch_size,
            "learning_rate": lr,
            "weight_decay": weight_decay,
            "early_stop_patience": early_stop_patience,
            "warmup_steps": warmup_steps,
            "max_grad_norm": max_grad_norm,
            "seed": seed,
            "ablation": "no_qformer_in_message_passing",
            "embedding_size": embedding_size
        })

    os.makedirs(log_dir, exist_ok=True)

    # Initialize embedding loader (still using text-q-former for item features)
    loader = EmbeddingLoader(embedding_dir, item_meta_path, "movie_id", "title", text_qformer, qformer_ckpt, tokenizer, device)

    # Load and preprocess data
    train_df = pd.read_csv(train_path)
    train_df = train_df.sample(frac=1, random_state=seed).reset_index(drop=True)
    val_df = pd.read_csv(val_path)
    test_df = pd.read_csv(test_path)

    train_loader = DataLoader(CTRDataset(train_df), batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(CTRDataset(val_df), batch_size=batch_size)
    test_loader = DataLoader(CTRDataset(test_df), batch_size=batch_size)

    # Calculate expert output dimension as half of embedding size
    expert_output_dim = max(32, embedding_size // 2)

    # Initialize models without text-q-former in message passing
    # HGT - User input dim is 256, Item input dim is 256 (不使用text-q-former)
    hgt = ResLLMHGTEncoderNoQFormer(
        input_dim=256,  # 修改为256，因为不使用text-q-former
        hidden_dim=embedding_size,  # 使用传入的embedding_size
        num_layers=2,
        num_types=2,
        num_heads=min(4, max(1, embedding_size // 64)),  # Adjust heads based on embedding size
        dropout=0.1,
        device=device
    ).to(device)
    print(f"Initialized ResLLMHGTEncoderNoQFormer with input_dim=256, hidden_dim={embedding_size}")

    # R-GCN - Input dim is 256 for both user and item
    rgcn = ResLLMRGCNEncoderNoQFormer(
        input_dim=256,
        hidden_dim=embedding_size,  # 使用传入的embedding_size
        num_relations=2,
        num_layers=2,
        dropout=0.1,
        device=device
    ).to(device)
    print(f"Initialized ResLLMRGCNEncoderNoQFormer with input_dim=256, hidden_dim={embedding_size}")

    # Expert fusion model
    model = FocusedHybridExpertAdaptor(
        input_dim=embedding_size,  # 使用传入的embedding_size
        expert_output_dim=expert_output_dim,
        num_shared_experts=3,
        num_user_experts=3,
        num_item_experts=3,
        hidden_dim=expert_output_dim,
        dropout=0.1
    ).to(device)

    # Combine all parameters for optimization
    all_params = list(model.parameters()) + list(hgt.parameters()) + list(rgcn.parameters())

    # Optimizer with weight decay
    optimizer = torch.optim.AdamW(
        all_params,
        lr=lr,
        weight_decay=weight_decay
    )

    # Learning rate scheduler with warmup
    total_steps = len(train_loader) * epochs
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=warmup_steps,
        num_training_steps=total_steps
    )

    # Loss function
    criterion = nn.BCELoss()

    # Initialize tracking variables
    best_auc = 0
    best_accuracy = 0
    patience_counter = 0

    # Training loop
    for epoch in range(epochs):
        model.train()
        hgt.train()
        rgcn.train()

        total_loss = 0
        batch_losses = []

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")):
            user_ids = batch["user_id"]
            item_ids = batch["item_id"]
            labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)

            # Get HGT embeddings
            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)

            # Get RGCN embeddings
            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)

            # Extract user and item embeddings
            hgt_user_feat = hgt_out[:len(user_ids)]
            hgt_item_feat = hgt_out[len(user_ids):]
            rgcn_user_feat = rgcn_out[:len(user_ids)]
            rgcn_item_feat = rgcn_out[len(user_ids):]

            # Combine HGT and RGCN embeddings
            user_feat = hgt_user_feat + rgcn_user_feat
            item_feat = hgt_item_feat + rgcn_item_feat

            # Forward pass through the expert fusion model
            prob, _, _ = model(user_feat, item_feat)

            # Calculate loss
            loss = criterion(prob.squeeze(), labels)

            # Backward pass and optimization
            optimizer.zero_grad()
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(all_params, max_grad_norm)

            optimizer.step()
            scheduler.step()

            # Track loss
            batch_loss = loss.item()
            total_loss += batch_loss
            batch_losses.append(batch_loss)

            # Log to wandb every 10 batches
            if use_wandb and batch_idx % 10 == 0:
                wandb.log({
                    "batch": epoch * len(train_loader) + batch_idx,
                    "batch_loss": batch_loss,
                    "running_avg_loss": total_loss / (batch_idx + 1),
                    "learning_rate": scheduler.get_last_lr()[0]
                })

        # Evaluate on validation set
        val_metrics = evaluate(model, val_loader, loader, hgt, rgcn, device)

        # Calculate epoch metrics
        avg_epoch_loss = total_loss / len(train_loader)
        epoch_loss_std = np.std(batch_losses) if len(batch_losses) > 1 else 0

        # Log metrics
        if use_wandb:
            wandb.log({
                "epoch": epoch+1,
                "train_loss": avg_epoch_loss,
                "train_loss_std": epoch_loss_std,
                "val_auc": val_metrics["auc"],
                "val_logloss": val_metrics["logloss"],
                "val_accuracy": val_metrics["accuracy"]
            })

        print(f"[Epoch {epoch+1}/{epochs}] Train Loss: {avg_epoch_loss:.4f} (±{epoch_loss_std:.4f}) | "
              f"Val AUC: {val_metrics['auc']:.4f} | Val Accuracy: {val_metrics['accuracy']:.4f}")

        # Save best model based on AUC
        if val_metrics["auc"] > best_auc:
            best_auc = val_metrics["auc"]
            best_accuracy = val_metrics["accuracy"]
            patience_counter = 0

            # Save all model components
            torch.save({
                'model': model.state_dict(),
                'hgt': hgt.state_dict(),
                'rgcn': rgcn.state_dict(),
                'epoch': epoch,
                'val_auc': val_metrics["auc"],
                'val_accuracy': val_metrics["accuracy"]
            }, os.path.join(log_dir, "best_model_no_qformer.pt"))

            print(f"✓ New best model saved! AUC: {best_auc:.4f}, Accuracy: {best_accuracy:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= early_stop_patience:
                print(f"Early stopping at epoch {epoch+1}")
                break

    # Load best model for testing
    checkpoint = torch.load(os.path.join(log_dir, "best_model_no_qformer.pt"))
    model.load_state_dict(checkpoint['model'])
    hgt.load_state_dict(checkpoint['hgt'])
    rgcn.load_state_dict(checkpoint['rgcn'])

    # Test the model
    test_metrics = evaluate(model, test_loader, loader, hgt, rgcn, device)
    print(f"\n[Test Results] AUC: {test_metrics['auc']:.4f} | Accuracy: {test_metrics['accuracy']:.4f} | LogLoss: {test_metrics['logloss']:.4f}")

    if use_wandb:
        wandb.log({
            "test_auc": test_metrics["auc"],
            "test_logloss": test_metrics["logloss"],
            "test_accuracy": test_metrics["accuracy"]
        })
        wandb.finish()

    return test_metrics


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--train_path", type=str, required=True)
    parser.add_argument("--val_path", type=str, required=True)
    parser.add_argument("--test_path", type=str, required=True)
    parser.add_argument("--embedding_dir", type=str, required=True)
    parser.add_argument("--item_meta_path", type=str, required=True)
    parser.add_argument("--qformer_ckpt", type=str, required=True)
    parser.add_argument("--epochs", type=int, default=10)
    parser.add_argument("--batch_size", type=int, default=256)
    parser.add_argument("--lr", type=float, default=5e-4)
    parser.add_argument("--weight_decay", type=float, default=1e-5)
    parser.add_argument("--early_stop_patience", type=int, default=5)
    parser.add_argument("--warmup_steps", type=int, default=500)
    parser.add_argument("--max_grad_norm", type=float, default=1.0)
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--log_dir", type=str, default='./ctr_logs_no_qformer')
    parser.add_argument("--wandb_name", type=str, default="ctr_ablation_no_qformer")
    parser.add_argument("--device", type=str, default="cuda")
    parser.add_argument("--embedding_size", type=int, default=512)  # 添加embedding_size参数
    args = parser.parse_args()

    # Initialize tokenizer and text Q-Former
    tokenizer = AutoTokenizer.from_pretrained("/root/code/roberta-base")
    text_qformer = TextQFormer(768, 768, 32, 8)

    # Train model
    test_metrics = train_ctr_model_no_qformer(
        train_path=args.train_path,
        val_path=args.val_path,
        test_path=args.test_path,
        embedding_dir=args.embedding_dir,
        item_meta_path=args.item_meta_path,
        text_qformer=text_qformer,
        tokenizer=tokenizer,
        qformer_ckpt=args.qformer_ckpt,
        device=args.device,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        weight_decay=args.weight_decay,
        early_stop_patience=args.early_stop_patience,
        warmup_steps=args.warmup_steps,
        max_grad_norm=args.max_grad_norm,
        seed=args.seed,
        log_dir=args.log_dir,
        wandb_name=args.wandb_name,
        embedding_size=args.embedding_size  # 传递embedding_size参数
    )