#!/bin/bash

# 设置环境变量
export PYTHONPATH=/root/code/GraphLLM4CTR:$PYTHONPATH

# 设置数据路径
DATA_DIR=" /data/datasets/processed_datasets/movielens/"
MODEL_DIR="/data/datasets/processed_datasets/movielens/ablation_results"
CHECKPOINT_DIR="/data/datasets/processed_datasets/movielens/ablation_results"

# 设置训练参数
EPOCHS=10
BATCH_SIZE=128
LEARNING_RATE=0.002
WEIGHT_DECAY=1e-5
EARLY_STOP_PATIENCE=5
WARMUP_STEPS=500
MAX_GRAD_NORM=1.0
SEED=42
EMBEDDING_SIZE=512  # 明确设置embedding_size为512

# 设置日志和模型保存路径
LOG_DIR="/data/datasets/processed_datasets/movielens/ablation_results/logs"
WANDB_NAME="ctr_ablation_no_qformer"

# 创建日志目录
mkdir -p $LOG_DIR

# 切换到项目根目录
cd /root/code/GraphLLM4CTR

# 运行训练脚本
python -m ablation_study_q_former.train_ctr_no_qformer \
    --train_path $DATA_DIR/train.csv \
    --val_path $DATA_DIR/val.csv \
    --test_path $DATA_DIR/test.csv \
    --embedding_dir /data/datasets/processed_datasets/movielens/temperature_analysis/results/temp_0.05/aligned_embeddings_epoch1 \
    --item_meta_path $DATA_DIR/train.csv \
    --qformer_ckpt /data/datasets/processed_datasets/movielens/temperature_analysis/results/temp_0.05/qformer_epoch1.pt \
    --epochs $EPOCHS \
    --batch_size $BATCH_SIZE \
    --lr $LEARNING_RATE \
    --weight_decay $WEIGHT_DECAY \
    --early_stop_patience $EARLY_STOP_PATIENCE \
    --warmup_steps $WARMUP_STEPS \
    --max_grad_norm $MAX_GRAD_NORM \
    --seed $SEED \
    --log_dir $LOG_DIR \
    --wandb_name $WANDB_NAME \
    --embedding_size $EMBEDDING_SIZE