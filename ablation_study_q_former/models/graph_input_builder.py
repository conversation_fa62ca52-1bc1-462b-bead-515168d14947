"""
Graph input builder for ablation studies.
This is a copy of the original graph_input_builder.py with fixes for the ablation study.
"""

import torch


def build_rgcn_input(user_ids, item_ids, loader):
    """
    构造用于 R-GCN 的输入图结构：节点特征、边索引、边类型

    返回：
        x: Tensor [N, D]
        edge_index: LongTensor [2, E]
        edge_type: LongTensor [E]
    """
    device = loader.device

    # Get user and item embeddings
    user_x = loader.get_user_rgcn_input(user_ids)  # [U, D] or [U, T, D]
    item_x = loader.get_item_rgcn_input(item_ids)  # [I, D] or [I, T, D]

    # Reshape to 2D if needed
    if len(user_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        user_x = user_x[:, 0, :]  # Take the first token's embedding
    if len(item_x.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        item_x = item_x[:, 0, :]  # Take the first token's embedding

    # Ensure both user and item features have the same dimension
    if user_x.shape[1] != item_x.shape[1]:
        if user_x.shape[1] < item_x.shape[1]:
            user_x = torch.nn.functional.linear(user_x, torch.eye(user_x.shape[1], item_x.shape[1], device=device))
        else:
            item_x = torch.nn.functional.linear(item_x, torch.eye(item_x.shape[1], user_x.shape[1], device=device))

    # Simple normalization without complex error handling
    def simple_normalize(x):
        # Ensure input is float and contiguous
        x = x.float().contiguous()
        
        # Replace NaN and Inf with zeros
        x = torch.where(torch.isnan(x) | torch.isinf(x), torch.zeros_like(x), x)
        
        # Add small epsilon to prevent division by zero
        norm = torch.norm(x, p=2, dim=1, keepdim=True)
        norm = torch.clamp(norm, min=1e-8)
        
        # Normalize
        x = x / norm
        
        # Final check for NaN and Inf
        x = torch.where(torch.isnan(x) | torch.isinf(x), torch.zeros_like(x), x)
        
        return x

    # Normalize features
    user_x = simple_normalize(user_x)
    item_x = simple_normalize(item_x)

    # Concatenate features
    x = torch.cat([user_x, item_x], dim=0)  # [U+I, D]

    # Create edge indices
    num_users = len(user_ids)
    num_items = len(item_ids)
    total_nodes = num_users + num_items

    # Create sparse connections
    src_indices = []
    tgt_indices = []
    edge_type = []

    # Connect each user to every 10th item
    for i in range(num_users):
        for j in range(0, num_items, 10):
            src_indices.append(i)
            tgt_indices.append(num_users + j)
            edge_type.append(0)  # 关系类型 0：点击

    # Convert to tensors
    if not src_indices:
        edge_index = torch.zeros((2, 0), dtype=torch.long, device=device)
        edge_type = torch.zeros(0, dtype=torch.long, device=device)
    else:
        edge_index = torch.tensor([src_indices, tgt_indices], dtype=torch.long, device=device)
        edge_type = torch.tensor(edge_type, dtype=torch.long, device=device)

    return x, edge_index, edge_type


def build_hgt_input(user_ids, item_ids, loader):
    """
    构造用于 HGT 的输入图结构：节点特征字典、边索引、边类型、节点类型

    返回：
        x_dict: Dict[int, Tensor] - 节点类型到特征的映射
        edge_index: LongTensor [2, E]
        edge_type: LongTensor [E]
        node_type: LongTensor [N]
    """
    device = loader.device
    x_dict = {}
    node_type = {}
    global_idx = 0

    uid_to_gid = {}
    iid_to_gid = {}

    # Get user features and ensure they're on the correct device and data type
    user_feats = loader.get_user_hgt_input(user_ids).to(device).float()

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    if len(user_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        user_feats = user_feats[:, 0, :]  # Take the first token's embedding

    x_dict[0] = user_feats
    for uid in user_ids:
        uid = int(uid)
        node_type[global_idx] = 0
        uid_to_gid[uid] = global_idx
        global_idx += 1

    # Get item features and ensure they're on the correct device and data type
    item_feats = loader.get_item_hgt_input(item_ids).to(device).float()

    # Reshape to 2D: [batch_size, hidden_dim] by taking the first token's embedding
    if len(item_feats.shape) == 3:  # [batch_size, seq_len, hidden_dim]
        item_feats = item_feats[:, 0, :]  # Take the first token's embedding

    x_dict[1] = item_feats
    for iid in item_ids:
        iid = int(iid)
        node_type[global_idx] = 1
        iid_to_gid[iid] = global_idx
        global_idx += 1

    edge_index = []
    edge_type = []

    # Create a sparse connection pattern to reduce memory usage
    # Connect each user to a subset of items
    for uid in user_ids:
        for j, iid in enumerate(item_ids):
            if j % 10 == 0:  # Connect to every 10th item to reduce edge count
                u_idx = uid_to_gid[int(uid)]
                i_idx = iid_to_gid[int(iid)]
                # Ensure indices are within bounds
                if u_idx < len(user_ids) and i_idx < global_idx:
                    edge_index.append([u_idx, i_idx])
                    # Use a single integer for edge type
                    edge_type.append(0)  # user -> item (use a single type)

    # Convert to tensors and move to the correct device
    if not edge_index:  # Handle empty edge case
        edge_index = torch.zeros((2, 0), dtype=torch.long, device=device)
        edge_type = torch.zeros(0, dtype=torch.long, device=device)
        node_type_tensor = torch.tensor([node_type[i] for i in range(len(node_type))], dtype=torch.long).to(device)
    else:
        edge_index = torch.LongTensor(edge_index).t().contiguous().to(device)
        edge_type = torch.LongTensor(edge_type).to(device)
        node_type_tensor = torch.tensor([node_type[i] for i in range(len(node_type))], dtype=torch.long).to(device)

    # Print debug information
    print(f"\nDebug information:")
    print(f"Number of users: {len(user_ids)}")
    print(f"Number of items: {len(item_ids)}")
    print(f"Total nodes: {global_idx}")
    print(f"Edge index shape: {edge_index.shape}")
    print(f"Edge type shape: {edge_type.shape}")
    print(f"Node type shape: {node_type_tensor.shape}")
    if edge_index.numel() > 0:
        print(f"Edge index range: [{edge_index.min().item()}, {edge_index.max().item()}]")

    return x_dict, edge_index, edge_type, node_type_tensor
