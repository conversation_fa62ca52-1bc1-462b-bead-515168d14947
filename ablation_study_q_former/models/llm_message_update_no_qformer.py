"""
LLM message updater without text-q-former.
Used for ablation studies to evaluate the contribution of text-q-former in message passing.
"""

import torch
import torch.nn as nn
from transformers import RobertaConfig, RobertaModel

class LLMMessageUpdaterNoQFormer(nn.Module):
    def __init__(self, hidden_dim, num_layers=2):
        super().__init__()
        self.hidden_dim = hidden_dim
        
        # Initialize RoBERTa model for message updating
        config = RobertaConfig(
            hidden_size=hidden_dim,
            intermediate_size=hidden_dim * 4,
            num_attention_heads=4,
            num_hidden_layers=num_layers
        )
        self.model = RobertaModel(config)
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout(0.1)

    def forward(self, hidden_states, attention_mask=None):
        """
        Args:
            hidden_states: Tensor of shape [batch_size, hidden_dim]
            attention_mask: Optional tensor of shape [batch_size]
        """
        # Ensure input is contiguous
        hidden_states = hidden_states.contiguous()
        
        # Reshape input for RoBERTa: [batch_size, hidden_dim] -> [batch_size, 1, hidden_dim]
        hidden_states = hidden_states.unsqueeze(1)
        
        # Create attention mask if not provided
        if attention_mask is None:
            attention_mask = torch.ones(
                hidden_states.shape[0],
                hidden_states.shape[1],
                device=hidden_states.device
            )
        
        # Apply layer normalization
        hidden_states = self.layer_norm(hidden_states)
        
        # Process through RoBERTa model
        try:
            outputs = self.model(inputs_embeds=hidden_states, attention_mask=attention_mask)
            hidden_states = self.dropout(outputs.last_hidden_state)
        except Exception as e:
            print(f"Error in model processing: {e}")
            print(f"Input shape: {hidden_states.shape}")
            print(f"Attention mask shape: {attention_mask.shape}")
            # Fallback: return the input features if model processing fails
            hidden_states = hidden_states
        
        # Reshape back: [batch_size, 1, hidden_dim] -> [batch_size, hidden_dim]
        hidden_states = hidden_states.squeeze(1)
        
        return hidden_states