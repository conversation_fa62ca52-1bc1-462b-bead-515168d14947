"""
Embedding loader for ablation studies.
This is a copy of the original embedding_loader.py with modifications for the ablation study.
"""

import torch
import pandas as pd
import numpy as np
from transformers import RobertaModel, RobertaTokenizer
import os

class EmbeddingLoader:
    def __init__(self, device="cpu"):
        self.device = device
        self.user_emb_path = "/root/code/GraphLLM4CTR/data/embeddings/user_emb.pt"
        self.item_emb_path = "/root/code/GraphLLM4CTR/data/embeddings/item_emb.pt"
        self.item_meta_path = "/root/code/GraphLLM4CTR/data/item_meta.csv"
        self.qformer_checkpoint_path = "/root/code/GraphLLM4CTR/checkpoints/qformer.pt"
        
        # Load user and item embeddings
        self.user_emb = torch.load(self.user_emb_path, map_location=device)
        self.item_emb = torch.load(self.item_emb_path, map_location=device)
        
        # Load item metadata
        self.item_meta = pd.read_csv(self.item_meta_path)[['item_id', 'title']].dropna()
        
        # Initialize RoBERTa model and tokenizer
        self.tokenizer = RobertaTokenizer.from_pretrained('/root/code/roberta-base')
        self.roberta = RobertaModel.from_pretrained('/root/code/roberta-base').to(device)
        
        # Load Q-Former state (if exists)
        if os.path.exists(self.qformer_checkpoint_path):
            state = torch.load(self.qformer_checkpoint_path, map_location=device)
            self.qformer_state = state
        else:
            self.qformer_state = None

    def get_user_rgcn_input(self, user_ids):
        """Get user features for RGCN input"""
        return self.user_emb[user_ids].to(self.device)

    def get_item_rgcn_input(self, item_ids):
        """Get item features for RGCN input"""
        return self.item_emb[item_ids].to(self.device)

    def get_user_hgt_input(self, user_ids):
        """Get user features for HGT input"""
        return self.user_emb[user_ids].to(self.device)

    def get_item_hgt_input(self, item_ids):
        """Get item features for HGT input"""
        # Get item embeddings
        item_emb = self.item_emb[item_ids].to(self.device)
        
        # Get item titles
        item_titles = self.item_meta[self.item_meta['item_id'].isin(item_ids)]['title'].values
        
        # Tokenize titles
        inputs = self.tokenizer(item_titles, padding=True, truncation=True, return_tensors="pt")
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        # Get RoBERTa embeddings
        with torch.no_grad():
            outputs = self.roberta(**inputs)
            text_emb = outputs.last_hidden_state[:, 0, :]  # Use [CLS] token
        
        # Concatenate item embedding and text embedding
        combined_emb = torch.cat([item_emb, text_emb], dim=1)
        
        return combined_emb 