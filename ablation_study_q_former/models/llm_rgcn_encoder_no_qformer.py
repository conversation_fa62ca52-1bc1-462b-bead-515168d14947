"""
LLM-enhanced RGCN encoder without text-q-former in message passing.
Used for ablation studies to evaluate the contribution of text-q-former in message passing.
"""

import torch
import torch.nn as nn
from .llm_rgcn_layer_no_qformer import ResLLMRGCNLayerNoQFormer

class ResLLMRGCNEncoderNoQFormer(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_relations, num_layers, dropout=0.1, device="cpu"):
        super().__init__()
        self.device = device
        self.num_layers = num_layers
        self.layers = nn.ModuleList()

        # Create RGCN layers
        for i in range(num_layers):
            self.layers.append(
                ResLLMRGCNLayerNoQFormer(
                    input_dim=input_dim if i == 0 else hidden_dim,
                    hidden_dim=hidden_dim,
                    num_relations=num_relations,
                    layer_idx=i,
                    num_layers=num_layers,
                    dropout=dropout,
                    device=device
                )
            )

    def forward(self, x, edge_index, edge_type):
        # Process through RGCN layers
        for layer in self.layers:
            x = layer(x, edge_index, edge_type)
        return x 