"""
LLM-enhanced RGCN layer without text-q-former in message passing.
Used for ablation studies to evaluate the contribution of text-q-former in message passing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing
from .llm_message_update_no_qformer import LLMMessageUpdaterNoQFormer

class ResLLMRGCNLayerNoQFormer(MessagePassing):
    def __init__(self, input_dim, hidden_dim, num_relations, layer_idx, num_layers, dropout=0.1, device="cpu"):
        super().__init__(aggr="add")
        self.device = device
        self.layer_idx = layer_idx
        self.dropout = dropout

        # Input projection - 只在第一层使用
        if layer_idx == 0:
            # 第一层：input_dim -> hidden_dim
            self.input_proj_256 = nn.Linear(256, hidden_dim)  # For 256-dim input
            self.input_proj_1024 = nn.Linear(1024, hidden_dim)  # For 1024-dim input
        else:
            # 后续层：hidden_dim -> hidden_dim
            self.input_proj = nn.Linear(hidden_dim, hidden_dim)

        # Relation-specific weight matrices
        self.weight = nn.Parameter(torch.Tensor(num_relations, hidden_dim, hidden_dim))
        nn.init.xavier_uniform_(self.weight)

        # Layer normalization
        self.layer_norm = nn.LayerNorm(hidden_dim)

        # LLM message updater without text-q-former
        self.msg_updater = LLMMessageUpdaterNoQFormer(
            hidden_dim=hidden_dim,
            num_layers=2
        )

        # Dropout layer
        self.dropout_layer = nn.Dropout(dropout)

    def forward(self, x, edge_index, edge_type):
        # Input projection
        if self.layer_idx == 0:
            x = self.input_proj_1024(x) if x.shape[1] == 1024 else self.input_proj_256(x)
        else:
            x = self.input_proj(x)

        # Message passing
        out = self.propagate(edge_index, x=x, edge_type=edge_type)

        # Apply layer norm and residual connection
        out = self.layer_norm(x + out)

        # Apply dropout
        out = self.dropout_layer(out)

        return out

    def message(self, x_j, edge_type):
        # Get relation-specific weight matrix
        weight = self.weight[edge_type]  # [E, D, D]
        
        # Apply relation-specific transformation
        out = torch.bmm(x_j.unsqueeze(1), weight).squeeze(1)  # [E, D]
        
        # Update message using LLM without text-q-former
        out = self.msg_updater(out)
        
        return out 