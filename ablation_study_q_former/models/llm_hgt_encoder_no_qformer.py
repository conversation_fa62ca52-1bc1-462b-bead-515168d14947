"""
LLM-enhanced HGT encoder without text-q-former in message passing.
Used for ablation studies to evaluate the contribution of text-q-former in message passing.
"""

import torch
import torch.nn as nn
from .llm_hgt_layer_no_qformer import ResLLMHGTLayerNoQFormer

class ResLLMHGTEncoderNoQFormer(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, num_types, num_heads=4, dropout=0.1, device="cpu"):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.num_types = num_types
        self.num_heads = num_heads
        self.device = device

        # Create HGT layers
        self.layers = nn.ModuleList([
            ResLLMHGTLayerNoQFormer(
                input_dim=input_dim if i == 0 else hidden_dim,
                hidden_dim=hidden_dim,
                num_types=num_types,
                layer_idx=i,
                num_layers=num_layers,
                num_heads=num_heads,
                dropout=dropout,
                device=device
            )
            for i in range(num_layers)
        ])

        # Final layer normalization
        self.final_norm = nn.LayerNorm(hidden_dim)

    def forward(self, x_dict, edge_index, edge_type, node_type):
        # Ensure all inputs are on the correct device
        edge_index = edge_index.to(self.device)
        edge_type = edge_type.to(self.device)
        node_type = node_type.to(self.device)

        if isinstance(x_dict, dict):
            x_dict = {k: v.to(self.device) for k, v in x_dict.items()}
        else:
            x_dict = x_dict.to(self.device)

        # Process through HGT layers
        for layer in self.layers:
            x_dict = layer(x_dict, edge_index, edge_type, node_type)

        # Apply final normalization if x_dict is a dictionary
        if isinstance(x_dict, dict):
            # Concatenate all node embeddings
            all_nodes = []
            for node_type_idx in sorted(x_dict.keys()):
                all_nodes.append(x_dict[node_type_idx])
            return torch.cat(all_nodes, dim=0)
        else:
            # If already a tensor, apply normalization directly
            return self.final_norm(x_dict)