import torch
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import torch.nn.functional as F
import torch.nn as nn

def reduce_dim(emb):
    if emb.dim() == 3:
        return emb.mean(dim=1)
    return emb

def safe_show(title="figure"):
    fname = title.lower().replace(" ", "_") + ".png"
    plt.savefig(fname)
    print(f"[Saved] {fname}")
    plt.close()

def visualize_tsne_simple(embedding_a, embedding_b, label_a='Item Graph', label_b='Item Text', num_points=3561, perplexity=5):
    # 截取并降维
    embedding_a = F.normalize(reduce_dim(embedding_a[1000:2000]), dim=-1).cpu()
    embedding_b = F.normalize(reduce_dim(embedding_b[1000:2000]), dim=-1).cpu()

    # 拼接并降维到2D
    
    emb = torch.cat([embedding_a, embedding_b], dim=0).detach().numpy()
    tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42).fit_transform(emb)

    # 可视化
    plt.figure(figsize=(10, 8))
    plt.scatter(tsne[:1000, 0], tsne[:1000, 1], c='blue', label=label_a, alpha=0.6, s=10)
    plt.scatter(tsne[1000:, 0], tsne[1000:, 1], c='red', label=label_b, alpha=0.6, s=10)
    plt.legend()
    plt.title("TSNE Embedding Visualization")
    plt.xlabel("TSNE-1")
    plt.ylabel("TSNE-2")
    plt.tight_layout()
    safe_show('TSNE_v2')


if __name__ == "__main__":
    item_text = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_text_proj.pt")
    item_click = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_clicked_proj.pt")
    item_imp = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_impressed_proj.pt")
    item_ = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_graph_proj.pt")
    # 求平均，与 user_text 进行可视化对比
    item_graph = (item_ + item_click + item_imp) / 2
    print('item_graph', item_graph.shape)
    print('item_text', item_text.shape)


    # before
    item_text_before = torch.load("/data/datasets/processed_datasets/movielens/text_embeddings/item_text_embeddings.pt")
    item_click_before = torch.load("/data/datasets/processed_datasets/movielens/graph_embeddings/item_co_clicked_item_graph_embeddings.pt")
    item_imp_before = torch.load("/data/datasets/processed_datasets/movielens/graph_embeddings/item_co_impressed_item_graph_embeddings.pt")
    item_graph_before = (item_click_before + item_imp_before) / 2
    # visualize_tsne(item_graph_before, item_text_before)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    reduce_text = nn.Linear(768, 64).cpu()  # 放 CPU
    item_text_before = item_text_before.cpu()
    item_text_before_reduced = reduce_text(item_text_before).detach()

    visualize_tsne_simple(item_graph, item_text, label_a="Item Graph", label_b="Item Text")
